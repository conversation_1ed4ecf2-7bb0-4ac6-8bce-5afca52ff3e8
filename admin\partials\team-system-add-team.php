<div class="wrap add-team-wrap">
    <!-- Modern Header Section -->
    <div class="add-team-header">
        <div class="header-content">
            <div class="header-info">
                <div class="header-icon">
                    <span class="dashicons dashicons-plus-alt"></span>
                </div>
                <div class="header-text">
                    <h1 class="add-team-title">
                        <span class="dashicons dashicons-groups"></span>
                        إنشاء فريق جديد
                    </h1>
                    <p class="add-team-subtitle">أنشئ فريقك الخاص وابدأ العمل الجماعي</p>
                </div>
            </div>
            <div class="header-actions">
                <a href="<?php echo admin_url('admin.php?page=team-system'); ?>" class="btn btn-outline">
                    <span class="dashicons dashicons-arrow-left-alt"></span>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Modern Form Container -->
    <div class="modern-form-container">
        <form id="add-team-form" method="post" enctype="multipart/form-data" class="add-team-form">
            <?php wp_nonce_field('team_system_add_team', 'team_system_nonce'); ?>

            <!-- Basic Information Section -->
            <div class="form-section">
                <div class="section-header">
                    <h3>
                        <span class="dashicons dashicons-admin-generic"></span>
                        المعلومات الأساسية
                    </h3>
                    <p>أدخل المعلومات الأساسية لفريقك الجديد</p>
                </div>

                <div class="form-grid">
                    <div class="form-group full-width">
                        <label for="team_name" class="form-label">
                            <span class="dashicons dashicons-edit"></span>
                            اسم الفريق
                        </label>
                        <input type="text" id="team_name" name="team_name" required class="form-input"
                               placeholder="أدخل اسم فريقك">
                    </div>

                    <div class="form-group full-width">
                        <label for="team_slug" class="form-label">
                            <span class="dashicons dashicons-admin-links"></span>
                            رابط الفريق
                        </label>

                        <!-- Status Message Outside Input -->
                        <div class="slug-status-message" id="slug-status-message">
                            <span class="status-icon"></span>
                            <span class="status-text"></span>
                        </div>

                        <div class="slug-input-container" id="slug-container">
                            <span class="slug-prefix"><?php echo home_url('/teams/'); ?></span>
                            <input type="text" id="team_slug" name="team_slug" class="form-input slug-input"
                                   pattern="[a-z0-9\-]+"
                                   placeholder="team-slug"
                                   title="يُسمح بالأحرف الإنجليزية الصغيرة والأرقام والشرطة فقط">
                            <div class="slug-loading" id="slug-loading">
                                <span class="dashicons dashicons-update"></span>
                            </div>
                        </div>

                        <div class="form-help">
                            <span class="dashicons dashicons-info"></span>
                            يُسمح بالأحرف الإنجليزية الصغيرة والأرقام والشرطة فقط. إذا تُرك فارغاً، سيتم إنشاؤه تلقائياً من اسم الفريق.
                        </div>

                        <div class="slug-preview" id="slug-preview">
                            <span class="dashicons dashicons-visibility"></span>
                            <span>رابط فريقك: </span>
                            <a href="#" target="_blank" id="preview-link"><?php echo home_url('/teams/your-team-slug'); ?></a>
                        </div>
                    </div>

                    <div class="form-group full-width">
                        <label for="team_description" class="form-label">
                            <span class="dashicons dashicons-text-page"></span>
                            وصف الفريق
                        </label>
                        <div class="editor-container">
                            <?php
                            wp_editor('', 'team_description', array(
                                'textarea_name' => 'team_description',
                                'media_buttons' => false,
                                'textarea_rows' => 8,
                                'teeny' => true,
                                'editor_class' => 'modern-editor'
                            ));
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Images and Media Section -->
            <div class="form-section">
                <div class="section-header">
                    <h3>
                        <span class="dashicons dashicons-format-image"></span>
                        الصور والشعارات
                    </h3>
                    <p>أضف شعار الفريق وصورة الغلاف</p>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">
                            <span class="dashicons dashicons-admin-appearance"></span>
                            شعار الفريق
                        </label>
                        <div class="image-upload-container">
                            <div class="current-image">
                                <div class="no-image-placeholder">
                                    <span class="dashicons dashicons-format-image"></span>
                                    <span>لا يوجد شعار</span>
                                </div>
                            </div>
                            <div class="upload-controls">
                                <input type="file" name="team_logo" id="team_logo" accept="image/*" class="file-input">
                                <label for="team_logo" class="btn btn-secondary">
                                    <span class="dashicons dashicons-upload"></span>
                                    اختيار شعار
                                </label>
                                <div class="upload-help">الحجم الموصى به: 200 × 200 بكسل</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <span class="dashicons dashicons-format-gallery"></span>
                            صورة الغلاف
                        </label>
                        <div class="image-upload-container">
                            <div class="current-image cover-image">
                                <div class="no-image-placeholder cover-placeholder">
                                    <span class="dashicons dashicons-format-image"></span>
                                    <span>لا توجد صورة غلاف</span>
                                </div>
                            </div>
                            <div class="upload-controls">
                                <input type="file" name="team_cover" id="team_cover" accept="image/*" class="file-input">
                                <label for="team_cover" class="btn btn-secondary">
                                    <span class="dashicons dashicons-upload"></span>
                                    اختيار صورة الغلاف
                                </label>
                                <div class="upload-help">الحجم الموصى به: 1200 × 400 بكسل</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Section -->
            <div class="form-submit-section">
                <div class="submit-container">
                    <div class="submit-actions">
                        <button type="submit" class="btn btn-primary btn-large">
                            <span class="dashicons dashicons-yes"></span>
                            إنشاء الفريق
                        </button>
                        <a href="<?php echo admin_url('admin.php?page=team-system'); ?>" class="btn btn-secondary">
                            <span class="dashicons dashicons-arrow-left-alt"></span>
                            إلغاء
                        </a>
                    </div>
                    <div class="submit-help">
                        <span class="dashicons dashicons-info"></span>
                        تأكد من صحة المعلومات قبل إنشاء الفريق
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
/* Modern Add Team Page Styling */
.add-team-wrap {
    background: #f8f9fa;
    margin: 0 -20px;
    padding: 0;
}

/* Hide WordPress footer in add team page */
#wpfooter {
    display: none !important;
}

/* Header Section */
.add-team-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    margin: 0 0 30px 0;
    position: relative;
    overflow: hidden;
}

.add-team-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 25px;
}

.header-icon {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255,255,255,0.3);
    flex-shrink: 0;
}

.header-icon .dashicons {
    font-size: 40px;
    width: 40px;
    height: 40px;
    color: white;
}

.header-text h1 {
    margin: 0 0 10px 0;
    font-size: 32px;
    font-weight: 700;
    color: white;
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-text h1 .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
}

.add-team-subtitle {
    margin: 0;
    font-size: 16px;
    color: rgba(255,255,255,0.9);
    line-height: 1.5;
}

.header-actions {
    display: flex;
    gap: 15px;
}

/* Modern Form Container */
.modern-form-container {
    margin: 0 40px 40px 40px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

/* Form Sections */
.form-section {
    padding: 40px;
    border-bottom: 1px solid #f0f0f1;
}

.form-section:last-child {
    border-bottom: none;
}

.section-header {
    margin-bottom: 30px;
    text-align: center;
}

.section-header h3 {
    margin: 0 0 10px 0;
    font-size: 24px;
    font-weight: 700;
    color: #1d2327;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.section-header h3 .dashicons {
    font-size: 28px;
    width: 28px;
    height: 28px;
    color: #667eea;
}

.section-header p {
    margin: 0;
    color: #6c757d;
    font-size: 16px;
    line-height: 1.5;
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.form-label .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #667eea;
}

.form-input,
.form-select {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-help {
    margin-top: 8px;
    font-size: 13px;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 6px;
}

.form-help .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    color: #9ca3af;
}

/* Slug Input with Availability Check */
.slug-input-container {
    display: flex;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.slug-input-container:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.slug-input-container.checking {
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.slug-input-container.available {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.slug-input-container.unavailable {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.slug-input-container.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.slug-prefix {
    background: #f9fafb;
    padding: 12px 16px;
    font-size: 14px;
    color: #6b7280;
    border-right: 1px solid #e5e7eb;
    white-space: nowrap;
    display: flex;
    align-items: center;
}

.slug-input {
    border: none !important;
    box-shadow: none !important;
    flex: 1;
    padding: 12px 16px;
    padding-right: 50px;
}

.slug-input:focus {
    outline: none;
    box-shadow: none;
}

/* Status Message Outside Input */
.slug-status-message {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 600;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.3s ease;
    min-height: 20px;
}

.slug-status-message.show {
    opacity: 1;
    transform: translateY(0);
}

.slug-status-message.available {
    color: #10b981;
}

.slug-status-message.unavailable {
    color: #ef4444;
}

.slug-status-message.error {
    color: #ef4444;
}

.slug-status-message.checking {
    color: #f59e0b;
}

.slug-status-message .status-icon {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    color: white;
    font-weight: bold;
    flex-shrink: 0;
}

.slug-status-message.available .status-icon {
    background: #10b981;
}

.slug-status-message.available .status-icon::before {
    content: "✓";
}

.slug-status-message.unavailable .status-icon {
    background: #ef4444;
}

.slug-status-message.unavailable .status-icon::before {
    content: "✗";
}

.slug-status-message.error .status-icon {
    background: #ef4444;
}

.slug-status-message.error .status-icon::before {
    content: "!";
}

.slug-status-message.checking .status-icon {
    background: #f59e0b;
    animation: pulse 1.5s ease-in-out infinite;
}

.slug-status-message.checking .status-icon::before {
    content: "⟳";
    animation: spin 1s linear infinite;
}

.slug-loading {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    display: none;
    color: #f59e0b;
}

.slug-loading.show {
    display: block;
}

.slug-loading .dashicons {
    animation: spin 1s linear infinite;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.slug-preview {
    margin-top: 8px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #0ea5e9;
    border-radius: 8px;
    display: none;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #0369a1;
}

.slug-preview.show {
    display: flex;
}

.slug-preview .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #0ea5e9;
}

.slug-preview a {
    color: #0369a1;
    text-decoration: none;
    font-weight: 600;
}

.slug-preview a:hover {
    text-decoration: underline;
}

/* Image Upload */
.image-upload-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.current-image {
    display: flex;
    justify-content: center;
}

.preview-image {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.logo-preview {
    width: 120px;
    height: 120px;
    object-fit: cover;
}

.cover-preview {
    max-width: 100%;
    height: auto;
    max-height: 200px;
    object-fit: cover;
}

.no-image-placeholder {
    width: 120px;
    height: 120px;
    background: #f3f4f6;
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #9ca3af;
    font-size: 13px;
}

.cover-placeholder {
    width: 100%;
    height: 150px;
}

.no-image-placeholder .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
}

.upload-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.file-input {
    display: none;
}

.upload-help {
    font-size: 12px;
    color: #6b7280;
    text-align: center;
}

/* Editor Container */
.editor-container {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.editor-container:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover:before {
    left: 100%;
}

.btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 2px solid #e9ecef;
}

.btn-secondary:hover {
    background: #e9ecef;
    color: #212529;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn-outline:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
    color: white;
}

.btn-large {
    padding: 16px 28px;
    font-size: 16px;
}

.btn-large .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

/* Submit Section */
.form-submit-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 30px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.submit-actions {
    display: flex;
    gap: 15px;
}

.submit-help {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 14px;
}

.submit-help .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #9ca3af;
}

/* Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-info {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .add-team-header {
        padding: 30px 20px;
    }

    .modern-form-container {
        margin-left: 20px;
        margin-right: 20px;
    }

    .form-section {
        padding: 30px 20px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-submit-section {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .submit-actions {
        flex-direction: column;
        width: 100%;
    }

    .submit-actions .btn {
        justify-content: center;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Hide WordPress footer
    $('#wpfooter').hide();

    var $teamName = $('#team_name');
    var $teamSlug = $('#team_slug');
    var $slugPreview = $('#slug-preview');
    var $previewLink = $('#preview-link');
    var $statusMessage = $('#slug-status-message');
    var $container = $('#slug-container');
    var $loading = $('#slug-loading');
    var slugTimeout;

    // Function to generate slug from team name
    function generateSlugFromName(name) {
        return name.toLowerCase()
            .replace(/[أ-ي]/g, '') // Remove Arabic characters
            .replace(/[^a-z0-9\s\-]/g, '') // Keep only English letters, numbers, spaces, and hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
            .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
    }

    // Function to validate slug format
    function validateSlug(slug) {
        return /^[a-z0-9\-]+$/.test(slug) && slug.length >= 3;
    }

    // Modern slug availability check function
    function checkSlugAvailability(slug) {
        // Clear previous timeout
        clearTimeout(slugTimeout);

        // Reset states
        $container.removeClass('checking available unavailable error');
        $statusMessage.removeClass('show available unavailable error checking');
        $loading.removeClass('show');
        $slugPreview.removeClass('show');

        // If empty, reset to default state
        if (!slug) {
            $previewLink.attr('href', '#').text('<?php echo home_url('/teams/your-team-slug'); ?>');
            return;
        }

        // Basic format validation
        if (!/^[a-z0-9\-]+$/.test(slug)) {
            $container.addClass('error');
            $statusMessage.addClass('show error');
            $statusMessage.find('.status-text').text('يُسمح بالأحرف الإنجليزية الصغيرة والأرقام والشرطة فقط');
            return;
        }

        // Check minimum length
        if (slug.length < 3) {
            $container.addClass('error');
            $statusMessage.addClass('show error');
            $statusMessage.find('.status-text').text('يجب أن يكون الرابط 3 أحرف على الأقل');
            return;
        }

        // Show loading state
        $container.addClass('checking');
        $statusMessage.addClass('show checking');
        $statusMessage.find('.status-text').text('جاري التحقق من توفر الرابط...');
        $loading.addClass('show');

        // Update preview
        var newUrl = '<?php echo home_url('/teams/'); ?>' + slug;
        $previewLink.attr('href', newUrl).text(newUrl);
        $slugPreview.addClass('show');

        // Debounced AJAX check
        slugTimeout = setTimeout(function() {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'check_team_slug_availability',
                    slug: slug,
                    nonce: '<?php echo wp_create_nonce('check_slug_nonce'); ?>'
                },
                success: function(response) {
                    $loading.removeClass('show');
                    $container.removeClass('checking');
                    $statusMessage.removeClass('checking');

                    if (response.success && response.data) {
                        if (response.data.available) {
                            $container.addClass('available');
                            $statusMessage.addClass('show available');
                            $statusMessage.find('.status-text').text(response.data.message);
                        } else {
                            $container.addClass('unavailable');
                            $statusMessage.addClass('show unavailable');
                            $statusMessage.find('.status-text').text(response.data.message);
                        }
                    } else {
                        $container.addClass('error');
                        $statusMessage.addClass('show error');
                        $statusMessage.find('.status-text').text('حدث خطأ أثناء التحقق من الرابط');
                    }
                },
                error: function() {
                    $loading.removeClass('show');
                    $container.removeClass('checking').addClass('error');
                    $statusMessage.removeClass('checking').addClass('show error');
                    $statusMessage.find('.status-text').text('خطأ في الاتصال بالخادم');
                }
            });
        }, 500);
    }

    // Auto-generate slug from team name if slug field is empty
    $teamName.on('input', function() {
        if (!$teamSlug.val()) {
            var generatedSlug = generateSlugFromName($(this).val());
            if (generatedSlug) {
                $teamSlug.val(generatedSlug);
                checkSlugAvailability(generatedSlug);
            }
        }
    });

    // Validate and check slug when user types in slug field
    $teamSlug.on('input', function() {
        var slug = $(this).val().toLowerCase();
        $(this).val(slug); // Force lowercase
        checkSlugAvailability(slug);
    });

    // Prevent invalid characters
    $teamSlug.on('keypress', function(e) {
        var char = String.fromCharCode(e.which);
        if (!/[a-z0-9\-]/.test(char) && e.which !== 8 && e.which !== 0) {
            e.preventDefault();

            // Show brief error indication
            $container.addClass('invalid-char');
            $statusMessage.addClass('show error');
            $statusMessage.find('.status-text').text('هذا الحرف غير مسموح');

            setTimeout(function() {
                $container.removeClass('invalid-char');
                $statusMessage.removeClass('show error');
                $statusMessage.find('.status-text').text('');
            }, 1500);
        }
    });

    // File input preview
    $('#team_logo').on('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                var $preview = $('.logo-preview');
                if ($preview.length) {
                    $preview.attr('src', e.target.result);
                } else {
                    $('.no-image-placeholder').first().replaceWith(
                        '<img src="' + e.target.result + '" class="preview-image logo-preview" alt="شعار الفريق">'
                    );
                }
            };
            reader.readAsDataURL(file);
        }
    });

    $('#team_cover').on('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                var $preview = $('.cover-preview');
                if ($preview.length) {
                    $preview.attr('src', e.target.result);
                } else {
                    $('.cover-placeholder').replaceWith(
                        '<img src="' + e.target.result + '" class="preview-image cover-preview" alt="صورة الغلاف">'
                    );
                }
            };
            reader.readAsDataURL(file);
        }
    });

    // Enhanced form validation
    $('#add-team-form').on('submit', function(e) {
        var teamName = $teamName.val().trim();
        var teamSlug = $teamSlug.val().trim();

        if (!teamName) {
            alert('يرجى إدخال اسم الفريق');
            e.preventDefault();
            return false;
        }

        if (teamSlug && !validateSlug(teamSlug)) {
            alert('رابط الفريق يجب أن يحتوي على أحرف إنجليزية صغيرة وأرقام وشرطات فقط، وأن يكون 3 أحرف على الأقل');
            e.preventDefault();
            return false;
        }

        if (teamSlug && teamSlug.length < 3) {
            alert('رابط الفريق يجب أن يكون 3 أحرف على الأقل');
            e.preventDefault();
            return false;
        }

        // Check if slug is available before submitting
        if ($container.hasClass('unavailable')) {
            alert('رابط الفريق غير متاح، يرجى اختيار رابط آخر');
            e.preventDefault();
            return false;
        }
    });

    // Add CSS for animations and effects
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
                20%, 40%, 60%, 80% { transform: translateX(2px); }
            }

            .slug-input-container.invalid-char {
                animation: shake 0.3s ease-in-out;
                border-color: #ef4444 !important;
            }

            .slug-status-message.show {
                animation: slideInDown 0.3s ease-out;
            }

            @keyframes slideInDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `)
        .appendTo('head');
});
</script>