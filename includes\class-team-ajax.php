<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Team_System_Ajax {

    public function __construct() {
        add_action('wp_ajax_create_team', array($this, 'create_team'));
        add_action('wp_ajax_invite_member', array($this, 'invite_member'));
    }

    public function create_team() {
        // Verify nonce and permissions
        check_ajax_referer('team_nonce', 'security');
        
        if (!current_user_can('translator')) {
            wp_send_json_error('ليس لديك الصلاحية لإنشاء فريق');
        }

        // Process team creation
        $team_data = array(
            'name' => sanitize_text_field($_POST['team_name']),
            'description' => sanitize_textarea_field($_POST['team_description']),
            'created_by' => get_current_user_id(),
            'created_at' => current_time('mysql')
        );

        // Save team to database and return response
        wp_send_json_success('تم إنشاء الفريق بنجاح');
    }

    public function invite_member() {
        // Similar implementation for member invitations
    }
}

new Team_System_Ajax();
