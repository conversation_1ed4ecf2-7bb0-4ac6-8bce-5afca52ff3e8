<?php
/**
 * Team System Theme Helper
 * 
 * Add this code to your theme's custom-roles.php file to allow team members
 * to edit team chapters while maintaining security for other posts.
 */

/**
 * Check if user can edit a specific team chapter
 */
function can_user_edit_team_chapter($user_id, $post_id) {
    // Check if this is a team chapter
    $publish_as_team = get_post_meta($post_id, '_publish_as_team', true);
    if ($publish_as_team !== '1') {
        return false; // Not a team chapter
    }

    $team_id = get_post_meta($post_id, '_team_id', true);
    if (!$team_id) {
        return false; // No team ID
    }

    // Check if user is team member
    global $wpdb;
    $count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM {$wpdb->prefix}team_members 
         WHERE user_id = %d AND team_id = %d AND status = 'active'",
        intval($user_id), intval($team_id)
    ));

    return $count > 0;
}

/**
 * Modified sekai_restrict_admin_access function
 * 
 * Replace your existing sekai_restrict_admin_access function with this one:
 */
function sekai_restrict_admin_access() {
    // Get current user
    $current_user = wp_get_current_user();
    
    // Allow administrators
    if (current_user_can('manage_options')) {
        return;
    }
    
    // Check if we're trying to edit a specific post
    if (isset($_GET['post']) && isset($_GET['action']) && $_GET['action'] === 'edit') {
        $post_id = intval($_GET['post']);
        $post = get_post($post_id);
        
        if ($post && $post->post_type === 'chapter') {
            // Check if user can edit this team chapter
            if (can_user_edit_team_chapter($current_user->ID, $post_id)) {
                return; // Allow access to team chapter
            }
        }
    }
    
    // Your existing allowed pages list
    $allowed_pages = array(
        'team-system',
        'team-system-add',
        // Add other allowed pages here
    );
    
    // Check if current page is in allowed list
    $current_page = isset($_GET['page']) ? $_GET['page'] : '';
    if (in_array($current_page, $allowed_pages)) {
        return; // Allow access to team system pages
    }
    
    // Check if user has translator role and is accessing allowed areas
    if (in_array('translator', $current_user->roles)) {
        // Allow access to specific admin areas for translators
        $allowed_admin_pages = array(
            'edit.php', // Posts list
            'post.php', // Edit post (will be filtered by team membership)
            'post-new.php', // New post
            'admin.php', // Admin pages (will be filtered by page parameter)
        );
        
        global $pagenow;
        if (in_array($pagenow, $allowed_admin_pages)) {
            return; // Allow access
        }
    }
    
    // If we reach here, redirect away from admin
    wp_redirect(home_url());
    exit;
}

/**
 * Instructions for implementation:
 * 
 * 1. Open your theme file: wp-content/themes/Sekaiplus/inc/custom-roles.php
 * 
 * 2. Find the existing sekai_restrict_admin_access() function
 * 
 * 3. Replace it with the modified version above
 * 
 * 4. Add the can_user_edit_team_chapter() function at the top of the file
 * 
 * 5. Make sure the function is called with add_action('admin_init', 'sekai_restrict_admin_access');
 */
