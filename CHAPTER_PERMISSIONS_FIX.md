# إصلاح مشكلة صلاحيات تعديل الفصول

## المشكلة
كان المترجمون قادرين على تعديل جميع الفصول في النظام، بما في ذلك الفصول التي لا تنتمي لفرقهم أو الفصول الفردية لمستخدمين آخرين.

## السبب
المشكلة كانت في منح صلاحيات عامة للمترجمين مثل:
- `edit_posts`
- `edit_others_posts` 
- `edit_published_posts`

هذه الصلاحيات تسمح بتعديل جميع المنشورات من نوع `chapter` بدلاً من تقييد التعديل على فصول الفريق فقط.

## الحل المطبق

### 1. إزالة الصلاحيات العامة
تم إزالة منح الصلاحيات العامة للمترجمين في:
- `admin/class-team-system-admin.php` - دالة `add_translator_capabilities()`
- `includes/class-team-system-activator.php` - دالة `activate()`

### 2. إضافة نظام تحكم صارم
تم إضافة دالة `restrict_chapter_editing_to_team_members()` التي:
- تعمل بأولوية عالية جداً (priority 0)
- تمنع تعديل الفصول للمستخدمين غير المخولين
- تسمح فقط بـ:
  - للإداريين: تعديل جميع الفصول
  - لأعضاء الفريق: تعديل فصول فريقهم فقط
  - للمؤلفين: تعديل فصولهم الفردية فقط

### 3. تحسين نظام الصلاحيات الموجود
تم تعديل الدوال التالية لتمنح صلاحيات محددة فقط:
- `enhance_team_member_capabilities()`
- `setup_team_member_capabilities()`
- `setup_global_team_capabilities()`
- `setup_team_edit_capabilities()`

بدلاً من منح صلاحيات عامة، تمنح الآن صلاحيات محددة لكل فصل على حدة.

## الملفات المعدلة

### admin/class-team-system-admin.php
- إضافة `restrict_chapter_editing_to_team_members()` 
- تعديل `add_translator_capabilities()`
- تعديل `enhance_team_member_capabilities()`
- تعديل `setup_team_member_capabilities()`
- تعديل `setup_global_team_capabilities()`
- تعديل `setup_team_edit_capabilities()`

### includes/class-team-system-activator.php
- تعديل دالة `activate()` لإزالة منح `edit_posts` للمترجمين

## ملفات الاختبار المضافة

### test-chapter-permissions.php
ملف اختبار شامل لفحص صلاحيات تعديل الفصول لمستخدم مترجم معين.

### debug-permissions.php
ملف تصحيح بسيط لفحص صلاحيات المستخدم الحالي.

## كيفية الاختبار

1. قم بتسجيل الدخول كمترجم
2. اذهب إلى قائمة الفصول في wp-admin
3. تأكد من أنك تستطيع تعديل فصول فريقك فقط
4. تأكد من أنك لا تستطيع تعديل فصول الفرق الأخرى
5. تأكد من أنك لا تستطيع تعديل الفصول الفردية للمستخدمين الآخرين

## ملاحظات مهمة

- الإداريون يحتفظون بصلاحية تعديل جميع الفصول
- أعضاء الفريق يمكنهم تعديل فصول فريقهم فقط
- المؤلفون يمكنهم تعديل فصولهم الفردية فقط
- النظام يدعم كلاً من `status` و `is_active` في جدول `team_members`

## التحقق من النجاح

بعد تطبيق هذه التغييرات، يجب أن:
1. لا يتمكن المترجمون من رؤية روابط "تعديل" للفصول التي لا يملكون صلاحية تعديلها
2. إذا حاولوا الوصول مباشرة لصفحة التعديل، سيتم منعهم
3. ستظهر رسائل خطأ مناسبة عند محاولة التعديل غير المصرح به

## استكشاف الأخطاء

إذا استمرت المشكلة:
1. تأكد من تحديث الإضافة
2. امسح أي cache موجود
3. تحقق من سجلات الأخطاء في WordPress
4. استخدم ملفات الاختبار المرفقة للتشخيص
