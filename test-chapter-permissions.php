<?php
/**
 * Test script to check chapter editing permissions
 * This file helps debug the team chapter editing system
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only run for administrators
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo "<h1>اختبار صلاحيات تعديل الفصول</h1>";

// Get current user
$current_user_id = get_current_user_id();
$current_user = wp_get_current_user();

echo "<h2>معلومات المستخدم الحالي</h2>";
echo "<p><strong>ID:</strong> $current_user_id</p>";
echo "<p><strong>الاسم:</strong> " . $current_user->display_name . "</p>";
echo "<p><strong>الدور:</strong> " . implode(', ', $current_user->roles) . "</p>";

// Test with a specific user (translator)
echo "<h2>اختبار صلاحيات مترجم معين</h2>";

// Get a translator user
$translator_users = get_users(array('role' => 'translator', 'number' => 1));
if (!empty($translator_users)) {
    $translator = $translator_users[0];
    echo "<p><strong>مترجم للاختبار:</strong> " . $translator->display_name . " (ID: " . $translator->ID . ")</p>";
    
    // Get some chapters
    $chapters = get_posts(array(
        'post_type' => 'chapter',
        'posts_per_page' => 10,
        'post_status' => 'any'
    ));
    
    echo "<h3>اختبار الصلاحيات على الفصول</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID الفصل</th><th>عنوان الفصل</th><th>نوع النشر</th><th>الفريق</th><th>يمكن التعديل؟</th><th>السبب</th></tr>";
    
    foreach ($chapters as $chapter) {
        $publish_as_team = get_post_meta($chapter->ID, '_publish_as_team', true);
        $team_id = get_post_meta($chapter->ID, '_team_id', true);
        
        // Check if translator can edit this chapter
        $can_edit = user_can($translator->ID, 'edit_post', $chapter->ID);
        
        $publish_type = ($publish_as_team === '1') ? 'فريق' : 'فردي';
        $team_name = '';
        $reason = '';
        
        if ($publish_as_team === '1' && $team_id) {
            global $wpdb;
            $team = $wpdb->get_row($wpdb->prepare(
                "SELECT name FROM {$wpdb->prefix}teams WHERE id = %d",
                $team_id
            ));
            $team_name = $team ? $team->name : 'فريق غير موجود';
            
            // Check if translator is team member
            $is_member = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}team_members 
                 WHERE user_id = %d AND team_id = %d 
                 AND (status = 'active' OR status IS NULL OR is_active = 1)",
                $translator->ID, $team_id
            ));
            
            $reason = $is_member ? 'عضو في الفريق' : 'ليس عضو في الفريق';
        } else {
            $reason = ($chapter->post_author == $translator->ID) ? 'مؤلف الفصل' : 'ليس مؤلف الفصل';
        }
        
        $can_edit_text = $can_edit ? '<span style="color: green;">نعم</span>' : '<span style="color: red;">لا</span>';
        
        echo "<tr>";
        echo "<td>" . $chapter->ID . "</td>";
        echo "<td>" . esc_html($chapter->post_title) . "</td>";
        echo "<td>" . $publish_type . "</td>";
        echo "<td>" . esc_html($team_name) . "</td>";
        echo "<td>" . $can_edit_text . "</td>";
        echo "<td>" . $reason . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>لم يتم العثور على أي مترجمين للاختبار.</p>";
}

echo "<h2>اختبار الصلاحيات العامة</h2>";
if (!empty($translator_users)) {
    $translator = $translator_users[0];
    
    echo "<h3>الصلاحيات العامة للمترجم</h3>";
    $general_caps = array('edit_posts', 'edit_others_posts', 'edit_published_posts', 'edit_private_posts');
    
    echo "<ul>";
    foreach ($general_caps as $cap) {
        $has_cap = user_can($translator->ID, $cap);
        $status = $has_cap ? '<span style="color: green;">نعم</span>' : '<span style="color: red;">لا</span>';
        echo "<li><strong>$cap:</strong> $status</li>";
    }
    echo "</ul>";
}

echo "<p><em>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</em></p>";
