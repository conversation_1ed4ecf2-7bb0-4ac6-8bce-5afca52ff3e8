<?php
global $wpdb;
$team_id = $atts['id'];
$team_slug = $atts['slug'];

// Get team data
$team = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}teams WHERE id = %d OR slug = %s",
    $team_id,
    $team_slug
));

if (!$team) {
    echo '<p>لم يتم العثور على الفريق المطلوب.</p>';
    return;
}

// Get team members
$members = $wpdb->get_results($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}team_members 
     WHERE team_id = %d AND is_active = 1 
     ORDER BY 
         CASE 
             WHEN role = 'leader' THEN 1
             WHEN role = 'translator' THEN 2
             WHEN role = 'editor' THEN 3
             WHEN role = 'reviewer' THEN 4
             WHEN role = 'designer' THEN 5
             ELSE 6
         END",
    $team->id
));
?>

<div class="team-profile">
    <div class="team-cover" style="background-image: url('<?php echo esc_url($team->cover_url); ?>');">
        <?php if ($team->logo_url) : ?>
            <div class="team-logo">
                <img src="<?php echo esc_url($team->logo_url); ?>" alt="<?php echo esc_attr($team->name); ?>">
            </div>
        <?php endif; ?>
    </div>
    
    <div class="team-info">
        <h1 class="team-name"><?php echo esc_html($team->name); ?></h1>
        
        <?php if ($team->description) : ?>
            <div class="team-description">
                <?php echo wpautop(wp_kses_post($team->description)); ?>
            </div>
        <?php endif; ?>
        
        <div class="team-meta">
            <?php if ($team->website_url) : ?>
                <a href="<?php echo esc_url($team->website_url); ?>" target="_blank" class="team-website">
                    <i class="fas fa-globe"></i> الموقع الإلكتروني
                </a>
            <?php endif; ?>
            
            <?php if ($team->facebook_url) : ?>
                <a href="<?php echo esc_url($team->facebook_url); ?>" target="_blank" class="team-facebook">
                    <i class="fab fa-facebook"></i> فيسبوك
                </a>
            <?php endif; ?>
            
            <?php if ($team->twitter_url) : ?>
                <a href="<?php echo esc_url($team->twitter_url); ?>" target="_blank" class="team-twitter">
                    <i class="fab fa-twitter"></i> تويتر
                </a>
            <?php endif; ?>
            
            <?php if ($team->discord_url) : ?>
                <a href="<?php echo esc_url($team->discord_url); ?>" target="_blank" class="team-discord">
                    <i class="fab fa-discord"></i> ديسكورد
                </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (!empty($members)) : ?>
        <div class="team-members">
            <h2>أعضاء الفريق</h2>
            <div class="members-grid">
                <?php foreach ($members as $member) : 
                    $user = get_userdata($member->user_id);
                    if (!$user) continue;
                    
                    $role_name = '';
                    switch ($member->role) {
                        case 'leader':
                            $role_name = 'قائد الفريق';
                            $role_class = 'leader';
                            break;
                        case 'translator':
                            $role_name = 'مترجم';
                            $role_class = 'translator';
                            break;
                        case 'editor':
                            $role_name = 'مدقق لغوي';
                            $role_class = 'editor';
                            break;
                        case 'reviewer':
                            $role_name = 'مراجع جودة';
                            $role_class = 'reviewer';
                            break;
                        case 'designer':
                            $role_name = 'مُصمم رسوم';
                            $role_class = 'designer';
                            break;
                        default:
                            $role_name = 'عضو';
                            $role_class = 'member';
                    }
                ?>
                    <div class="team-member">
                        <div class="member-avatar">
                            <?php echo get_avatar($user->ID, 150); ?>
                            <span class="member-role <?php echo esc_attr($role_class); ?>"><?php echo esc_html($role_name); ?></span>
                        </div>
                        <h3 class="member-name"><?php echo esc_html($user->display_name); ?></h3>
                        <div class="member-joined">
                            انضم في <?php echo date_i18n('j F Y', strtotime($member->joined_at)); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.team-profile {
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.team-cover {
    height: 300px;
    background-size: cover;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.team-logo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.team-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.team-info {
    padding: 30px;
    text-align: center;
}

.team-name {
    margin: 0 0 20px;
    color: #333;
    font-size: 2.5em;
}

.team-description {
    max-width: 800px;
    margin: 0 auto 30px;
    line-height: 1.8;
    color: #555;
}

.team-meta {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.team-meta a {
    display: inline-flex;
    align-items: center;
    padding: 8px 15px;
    background: #f5f5f5;
    border-radius: 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.team-meta a:hover {
    background: #e0e0e0;
    transform: translateY(-2px);
}

.team-meta i {
    margin-left: 5px;
}

.team-members {
    padding: 0 30px 40px;
}

.team-members h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 2em;
}

.members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 25px;
}

.team-member {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    text-align: center;
    padding: 20px;
}

.team-member:hover {
    transform: translateY(-5px);
}

.member-avatar {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 15px;
}

.member-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.member-role {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%) translateY(50%);
    background: #4CAF50;
    color: white;
    padding: 3px 10px;
    border-radius: 15px;
    font-size: 12px;
    white-space: nowrap;
}

.member-role.leader { background: #e74c3c; }
.member-role.translator { background: #3498db; }
.member-role.editor { background: #9b59b6; }
.member-role.reviewer { background: #f39c12; }
.member-role.designer { background: #1abc9c; }

.member-name {
    margin: 15px 0 5px;
    color: #333;
    font-size: 1.2em;
}

.member-joined {
    color: #777;
    font-size: 0.9em;
}

/* Responsive styles */
@media (max-width: 768px) {
    .members-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }
    
    .team-cover {
        height: 200px;
    }
    
    .team-logo {
        width: 120px;
        height: 120px;
    }
    
    .team-name {
        font-size: 2em;
    }
}

@media (max-width: 480px) {
    .members-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .team-meta {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
}
</style>