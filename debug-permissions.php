<?php
/**
 * Debug script to check chapter editing permissions
 * Run this in WordPress admin to test the permission system
 */

// Only run for logged-in users
if (!is_user_logged_in()) {
    die('يجب تسجيل الدخول أولاً');
}

echo "<h1>تصحيح صلاحيات تعديل الفصول</h1>";

$current_user_id = get_current_user_id();
$current_user = wp_get_current_user();

echo "<h2>المستخدم الحالي</h2>";
echo "<p><strong>ID:</strong> $current_user_id</p>";
echo "<p><strong>الاسم:</strong> " . $current_user->display_name . "</p>";
echo "<p><strong>الأدوار:</strong> " . implode(', ', $current_user->roles) . "</p>";

// Test general capabilities
echo "<h2>الصلاحيات العامة</h2>";
$general_caps = array('edit_posts', 'edit_others_posts', 'edit_published_posts', 'manage_options');
foreach ($general_caps as $cap) {
    $has_cap = current_user_can($cap);
    $status = $has_cap ? '<span style="color: green;">✓</span>' : '<span style="color: red;">✗</span>';
    echo "<p>$status <strong>$cap</strong></p>";
}

// Get some chapters to test
$chapters = get_posts(array(
    'post_type' => 'chapter',
    'posts_per_page' => 5,
    'post_status' => 'any'
));

if (!empty($chapters)) {
    echo "<h2>اختبار الفصول</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>ID</th>";
    echo "<th style='padding: 8px;'>العنوان</th>";
    echo "<th style='padding: 8px;'>المؤلف</th>";
    echo "<th style='padding: 8px;'>نوع النشر</th>";
    echo "<th style='padding: 8px;'>الفريق</th>";
    echo "<th style='padding: 8px;'>يمكن التعديل؟</th>";
    echo "</tr>";
    
    foreach ($chapters as $chapter) {
        $publish_as_team = get_post_meta($chapter->ID, '_publish_as_team', true);
        $team_id = get_post_meta($chapter->ID, '_team_id', true);
        
        // Test if current user can edit this chapter
        $can_edit = current_user_can('edit_post', $chapter->ID);
        
        $author = get_user_by('ID', $chapter->post_author);
        $author_name = $author ? $author->display_name : 'غير معروف';
        
        $publish_type = ($publish_as_team === '1') ? 'فريق' : 'فردي';
        $team_name = '';
        
        if ($publish_as_team === '1' && $team_id) {
            global $wpdb;
            $team = $wpdb->get_row($wpdb->prepare(
                "SELECT name FROM {$wpdb->prefix}teams WHERE id = %d",
                $team_id
            ));
            $team_name = $team ? $team->name : 'فريق غير موجود';
        }
        
        $can_edit_text = $can_edit ? '<span style="color: green;">نعم ✓</span>' : '<span style="color: red;">لا ✗</span>';
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . $chapter->ID . "</td>";
        echo "<td style='padding: 8px;'>" . esc_html($chapter->post_title) . "</td>";
        echo "<td style='padding: 8px;'>" . esc_html($author_name) . "</td>";
        echo "<td style='padding: 8px;'>" . $publish_type . "</td>";
        echo "<td style='padding: 8px;'>" . esc_html($team_name) . "</td>";
        echo "<td style='padding: 8px;'>" . $can_edit_text . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>لا توجد فصول للاختبار.</p>";
}

// Show team memberships
echo "<h2>عضوية الفرق</h2>";
global $wpdb;
$user_teams = $wpdb->get_results($wpdb->prepare(
    "SELECT t.id, t.name, t.slug, tm.status, tm.is_active 
     FROM {$wpdb->prefix}teams t
     INNER JOIN {$wpdb->prefix}team_members tm ON t.id = tm.team_id
     WHERE tm.user_id = %d",
    $current_user_id
));

if (!empty($user_teams)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>اسم الفريق</th>";
    echo "<th style='padding: 8px;'>الرابط</th>";
    echo "<th style='padding: 8px;'>الحالة</th>";
    echo "</tr>";
    
    foreach ($user_teams as $team) {
        $status = '';
        if (isset($team->status)) {
            $status = $team->status;
        } elseif (isset($team->is_active)) {
            $status = $team->is_active ? 'active' : 'inactive';
        } else {
            $status = 'active'; // default
        }
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . esc_html($team->name) . "</td>";
        echo "<td style='padding: 8px;'>" . esc_html($team->slug) . "</td>";
        echo "<td style='padding: 8px;'>" . esc_html($status) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>لست عضواً في أي فريق.</p>";
}

echo "<hr>";
echo "<p><em>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</em></p>";
echo "<p><strong>ملاحظة:</strong> إذا كانت النتائج غير صحيحة، تأكد من أن التغييرات الأخيرة على نظام الصلاحيات تم تطبيقها بشكل صحيح.</p>";
?>
