<?php
class Team_System {
    protected $loader;
    protected $plugin_name;
    protected $version;

    public function __construct() {
        $this->version = TEAM_SYSTEM_VERSION;
        $this->plugin_name = 'team-system';
        $this->load_dependencies();
        $this->define_admin_hooks();
        $this->define_public_hooks();
    }

    private function load_dependencies() {
        // Load activator first
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-team-system-activator.php';
        
        // Load other dependencies
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-team-system-loader.php';
        require_once plugin_dir_path(dirname(__FILE__)) . 'admin/class-team-system-admin.php';
        require_once plugin_dir_path(dirname(__FILE__)) . 'public/class-team-system-public.php';
        
        $this->loader = new Team_System_Loader();
    }

    private function define_admin_hooks() {
        $plugin_admin = new Team_System_Admin($this->get_plugin_name(), $this->get_version());
        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_styles');
        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts');
    }

    private function define_public_hooks() {
        $plugin_public = new Team_System_Public($this->get_plugin_name(), $this->get_version());
        $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_styles');
        $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_scripts');
    }

    public function run() {
        $this->loader->run();
    }

    public function get_plugin_name() {
        return $this->plugin_name;
    }

    public function get_loader() {
        return $this->loader;
    }

    public function get_version() {
        return $this->version;
    }
}