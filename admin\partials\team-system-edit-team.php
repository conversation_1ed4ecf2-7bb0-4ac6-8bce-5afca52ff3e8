<?php
if (!defined('ABSPATH')) {
    exit;
}

// Get team data
$team_id = isset($_GET['team_id']) ? intval($_GET['team_id']) : 0;
$team = $this->get_team_by_id($team_id);

if (!$team) {
    echo '<div class="wrap"><div class="notice notice-error"><p>لم يتم العثور على الفريق المطلوب.</p></div></div>';
    return;
}

// Check if current user is team leader or admin
$current_user_id = get_current_user_id();
if (!current_user_can('manage_options') && $team->created_by != $current_user_id) {
    wp_die('ليس لديك صلاحية لتعديل هذا الفريق');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && check_admin_referer('update_team_' . $team_id)) {
    // Process and update team data
    $this->update_team($team_id, $_POST);
    $team = $this->get_team_by_id($team_id); // Refresh team data
    echo '<div class="notice notice-success"><p>تم تحديث بيانات الفريق بنجاح.</p></div>';
}
?>

<div class="wrap team-edit-wrap">
    <!-- Modern Header Section -->
    <div class="team-edit-header">
        <div class="header-content">
            <div class="team-header-info">
                <div class="team-logo-display">
                    <?php if ($team->logo_url): ?>
                        <img src="<?php echo esc_url($team->logo_url); ?>" alt="<?php echo esc_attr($team->name); ?>">
                    <?php else: ?>
                        <div class="team-logo-placeholder">
                            <span class="dashicons dashicons-groups"></span>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="team-header-text">
                    <h1 class="team-edit-title">
                        <span class="dashicons dashicons-edit"></span>
                        تعديل الفريق: <?php echo esc_html($team->name); ?>
                    </h1>
                    <p class="team-edit-subtitle">قم بتحديث معلومات فريقك وإدارة الأعضاء والإعدادات</p>
                </div>
            </div>
            <div class="header-actions">
                <a href="<?php echo home_url('/teams/' . $team->slug); ?>" class="btn btn-secondary" target="_blank">
                    <span class="dashicons dashicons-visibility"></span>
                    عرض الفريق
                </a>
                <a href="<?php echo admin_url('admin.php?page=team-system'); ?>" class="btn btn-outline">
                    <span class="dashicons dashicons-arrow-left-alt"></span>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Modern Tab Navigation -->
    <div class="modern-tabs">
        <div class="tab-navigation">
            <button class="tab-btn active" data-tab="team-info">
                <span class="dashicons dashicons-admin-generic"></span>
                <span class="tab-label">معلومات الفريق</span>
            </button>
            <button class="tab-btn" data-tab="team-members">
                <span class="dashicons dashicons-admin-users"></span>
                <span class="tab-label">إدارة الأعضاء</span>
                <span class="member-count"><?php echo count($this->get_team_members($team_id)); ?></span>
            </button>
            <button class="tab-btn" data-tab="team-settings">
                <span class="dashicons dashicons-admin-settings"></span>
                <span class="tab-label">الإعدادات</span>
            </button>
        </div>
    </div>

    <!-- Modern Form Container -->
    <div class="modern-form-container">
        <form method="post" enctype="multipart/form-data" class="team-edit-form">
            <?php wp_nonce_field('update_team_' . $team_id); ?>

            <!-- Team Info Tab -->
            <div id="team-info" class="tab-content active">
                <div class="form-section">
                    <div class="section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-generic"></span>
                            المعلومات الأساسية
                        </h3>
                        <p>قم بتحديث المعلومات الأساسية لفريقك</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group full-width">
                            <label for="team_name" class="form-label">
                                <span class="dashicons dashicons-edit"></span>
                                اسم الفريق
                            </label>
                            <input type="text" name="team_name" id="team_name" class="form-input"
                                   value="<?php echo esc_attr($team->name); ?>" required
                                   placeholder="أدخل اسم الفريق">
                        </div>

                        <div class="form-group full-width">
                            <label for="team_slug" class="form-label">
                                <span class="dashicons dashicons-admin-links"></span>
                                رابط الفريق
                            </label>

                            <!-- Status Message Outside Input -->
                            <div class="slug-status-message" id="slug-status-message">
                                <span class="status-icon"></span>
                                <span class="status-text"></span>
                            </div>

                            <div class="slug-input-container" id="slug-container">
                                <span class="slug-prefix"><?php echo home_url('/teams/'); ?></span>
                                <input type="text" name="team_slug" id="team_slug" class="form-input slug-input"
                                       value="<?php echo esc_attr($team->slug); ?>"
                                       pattern="[a-z0-9\-]+"
                                       placeholder="team-slug"
                                       data-current-team-id="<?php echo $team_id; ?>"
                                       title="يُسمح بالأحرف الإنجليزية الصغيرة والأرقام والشرطة فقط">
                                <div class="slug-loading" id="slug-loading">
                                    <span class="dashicons dashicons-update"></span>
                                </div>
                            </div>

                            <div class="form-help" id="slug-help">
                                <span class="dashicons dashicons-info"></span>
                                رابط فريقك الحالي: <a href="<?php echo home_url('/teams/' . $team->slug); ?>" target="_blank" id="current-team-link"><?php echo home_url('/teams/' . $team->slug); ?></a>
                            </div>

                            <div class="slug-preview" id="slug-preview">
                                <span class="dashicons dashicons-visibility"></span>
                                <span>الرابط الجديد: </span>
                                <a href="#" target="_blank" id="preview-link"></a>
                            </div>
                        </div>

                        <div class="form-group full-width">
                            <label for="team_description" class="form-label">
                                <span class="dashicons dashicons-text-page"></span>
                                وصف الفريق
                            </label>
                            <div class="editor-container">
                                <?php
                                wp_editor(
                                    wp_kses_post($team->description),
                                    'team_description',
                                    array(
                                        'textarea_name' => 'team_description',
                                        'media_buttons' => false,
                                        'textarea_rows' => 8,
                                        'teeny' => true,
                                        'editor_class' => 'modern-editor'
                                    )
                                );
                                ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-header">
                        <h3>
                            <span class="dashicons dashicons-format-image"></span>
                            الصور والشعارات
                        </h3>
                        <p>قم بتحديث شعار الفريق وصورة الغلاف</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">
                                <span class="dashicons dashicons-admin-appearance"></span>
                                شعار الفريق
                            </label>
                            <div class="image-upload-container">
                                <div class="current-image">
                                    <?php if ($team->logo_url): ?>
                                        <img src="<?php echo esc_url($team->logo_url); ?>" alt="شعار الفريق" class="preview-image logo-preview">
                                    <?php else: ?>
                                        <div class="no-image-placeholder">
                                            <span class="dashicons dashicons-format-image"></span>
                                            <span>لا يوجد شعار</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="upload-controls">
                                    <input type="file" name="team_logo" id="team_logo" accept="image/*" class="file-input">
                                    <label for="team_logo" class="btn btn-secondary">
                                        <span class="dashicons dashicons-upload"></span>
                                        اختيار شعار
                                    </label>
                                    <div class="upload-help">الحجم الموصى به: 200 × 200 بكسل</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <span class="dashicons dashicons-format-gallery"></span>
                                صورة الغلاف
                            </label>
                            <div class="image-upload-container">
                                <div class="current-image cover-image">
                                    <?php if ($team->cover_url): ?>
                                        <img src="<?php echo esc_url($team->cover_url); ?>" alt="صورة الغلاف" class="preview-image cover-preview">
                                    <?php else: ?>
                                        <div class="no-image-placeholder cover-placeholder">
                                            <span class="dashicons dashicons-format-image"></span>
                                            <span>لا توجد صورة غلاف</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="upload-controls">
                                    <input type="file" name="team_cover" id="team_cover" accept="image/*" class="file-input">
                                    <label for="team_cover" class="btn btn-secondary">
                                        <span class="dashicons dashicons-upload"></span>
                                        اختيار صورة الغلاف
                                    </label>
                                    <div class="upload-help">الحجم الموصى به: 1200 × 400 بكسل</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Team Members Tab -->
            <div id="team-members" class="tab-content">
                <div class="form-section">
                    <div class="section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-users"></span>
                            أعضاء الفريق الحاليون
                        </h3>
                        <p>إدارة أعضاء الفريق وأدوارهم</p>
                    </div>

                    <div class="members-grid">
                        <?php
                        $members = $this->get_team_members($team_id);
                        foreach ($members as $member) {
                            $user = get_userdata($member->user_id);
                            $role_name = $this->get_role_name($member->role);
                            ?>
                            <div class="member-card" data-user-id="<?php echo $member->user_id; ?>">
                                <div class="member-card-header">
                                    <div class="member-avatar-large">
                                        <?php echo get_avatar($user->ID, 80); ?>
                                        <?php if ($member->user_id == $team->created_by): ?>
                                            <div class="leader-crown">
                                                <span class="dashicons dashicons-star-filled"></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="member-info">
                                        <h4 class="member-name"><?php echo esc_html($user->display_name); ?></h4>
                                        <p class="member-email"><?php echo esc_html($user->user_email); ?></p>
                                        <div class="member-meta">
                                            <span class="join-date">
                                                <span class="dashicons dashicons-calendar-alt"></span>
                                                انضم في <?php echo date_i18n('j M Y', strtotime($member->joined_at)); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="member-card-body">
                                    <div class="member-role-section">
                                        <label class="role-label">
                                            <span class="dashicons dashicons-admin-users"></span>
                                            الدور في الفريق
                                        </label>
                                        <?php if ($member->user_id == $team->created_by): ?>
                                            <div class="leader-badge">
                                                <span class="dashicons dashicons-star-filled"></span>
                                                قائد الفريق
                                            </div>
                                        <?php else: ?>
                                            <select name="member_roles[<?php echo $member->user_id; ?>]" class="role-select">
                                                <?php foreach ($this->get_available_roles() as $role => $label): ?>
                                                    <?php if ($role !== 'leader'): ?>
                                                        <option value="<?php echo $role; ?>" <?php selected($member->role, $role); ?>>
                                                            <?php echo $label; ?>
                                                        </option>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </select>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="member-card-footer">
                                    <?php if ($member->user_id != $team->created_by): ?>
                                        <button type="button" class="btn btn-danger remove-member" data-user-id="<?php echo $member->user_id; ?>">
                                            <span class="dashicons dashicons-trash"></span>
                                            إزالة من الفريق
                                        </button>
                                    <?php else: ?>
                                        <div class="leader-info">
                                            <span class="dashicons dashicons-info"></span>
                                            لا يمكن إزالة قائد الفريق
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php
                        }
                        ?>
                    </div>
                </div>

                <!-- Pending Invitations Section -->
                <?php
                global $wpdb;
                $pending_invitations = $wpdb->get_results($wpdb->prepare(
                    "SELECT i.*, u.display_name, u.user_email
                     FROM {$wpdb->prefix}team_invitations i
                     LEFT JOIN {$wpdb->users} u ON i.user_id = u.ID
                     WHERE i.team_id = %d AND i.status = 'pending' AND i.expires_at > NOW()
                     ORDER BY i.created_at DESC",
                    $team_id
                ));

                if (!empty($pending_invitations)): ?>
                    <div class="form-section">
                        <div class="section-header">
                            <h3>
                                <span class="dashicons dashicons-clock"></span>
                                الدعوات المعلقة
                            </h3>
                            <p>الدعوات التي تم إرسالها وفي انتظار الرد</p>
                        </div>

                        <div class="invitations-grid">
                            <?php foreach ($pending_invitations as $invitation): ?>
                                <div class="invitation-card" data-invitation-id="<?php echo $invitation->id; ?>">
                                    <div class="invitation-header">
                                        <div class="invitation-avatar">
                                            <?php echo get_avatar($invitation->user_email, 60); ?>
                                            <div class="pending-indicator">
                                                <span class="dashicons dashicons-clock"></span>
                                            </div>
                                        </div>
                                        <div class="invitation-info">
                                            <h4 class="invitation-name"><?php echo esc_html($invitation->display_name); ?></h4>
                                            <p class="invitation-email"><?php echo esc_html($invitation->user_email); ?></p>
                                        </div>
                                    </div>

                                    <div class="invitation-body">
                                        <div class="invitation-details">
                                            <div class="detail-item">
                                                <span class="dashicons dashicons-admin-users"></span>
                                                <span>الدور: <?php echo esc_html($this->get_role_name($invitation->role)); ?></span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="dashicons dashicons-calendar-alt"></span>
                                                <span>أُرسلت: <?php echo date_i18n('j M Y', strtotime($invitation->created_at)); ?></span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="dashicons dashicons-warning"></span>
                                                <span>تنتهي: <?php echo date_i18n('j M Y', strtotime($invitation->expires_at)); ?></span>
                                            </div>
                                        </div>

                                        <div class="invitation-status">
                                            <span class="status-badge pending">
                                                <span class="dashicons dashicons-clock"></span>
                                                في انتظار الرد
                                            </span>
                                        </div>
                                    </div>

                                    <div class="invitation-footer">
                                        <button type="button" class="btn btn-danger cancel-invitation" data-invitation-id="<?php echo $invitation->id; ?>">
                                            <span class="dashicons dashicons-dismiss"></span>
                                            إلغاء الدعوة
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Add New Member Section -->
                <div class="form-section">
                    <div class="section-header">
                        <h3>
                            <span class="dashicons dashicons-plus-alt"></span>
                            دعوة أعضاء جدد
                        </h3>
                        <p>أضف أعضاء جدد إلى فريقك عن طريق إرسال دعوات</p>
                    </div>

                    <div class="add-member-container">
                        <div class="add-member-form">
                            <div class="form-group">
                                <label for="new_member_username" class="form-label">
                                    <span class="dashicons dashicons-admin-users"></span>
                                    اسم المستخدم أو البريد الإلكتروني
                                </label>
                                <input type="text" id="new_member_username" class="form-input"
                                       placeholder="أدخل اسم المستخدم أو البريد الإلكتروني">
                            </div>

                            <div class="form-group">
                                <label for="new_member_role" class="form-label">
                                    <span class="dashicons dashicons-admin-generic"></span>
                                    الدور في الفريق
                                </label>
                                <select id="new_member_role" class="form-select">
                                    <?php foreach ($this->get_available_roles() as $role => $label): ?>
                                        <?php if ($role !== 'leader'): ?>
                                            <option value="<?php echo $role; ?>"><?php echo $label; ?></option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <button type="button" id="add_member_btn" class="btn btn-primary btn-large">
                                    <span class="dashicons dashicons-email-alt"></span>
                                    إرسال دعوة
                                </button>
                            </div>
                        </div>

                        <div class="add-member-help">
                            <div class="help-item">
                                <span class="dashicons dashicons-info"></span>
                                <span>يمكنك إدخال اسم المستخدم (مثل: admin) أو البريد الإلكتروني</span>
                            </div>
                            <div class="help-item">
                                <span class="dashicons dashicons-email"></span>
                                <span>سيتم إرسال دعوة عبر البريد الإلكتروني للعضو الجديد</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Team Settings Tab -->
            <div id="team-settings" class="tab-content">
                <div class="form-section">
                    <div class="section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-links"></span>
                            الروابط الاجتماعية
                        </h3>
                        <p>أضف روابط الشبكات الاجتماعية والمواقع الخاصة بفريقك</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="team_website" class="form-label">
                                <span class="dashicons dashicons-admin-site"></span>
                                الموقع الإلكتروني
                            </label>
                            <input type="url" name="team_website" id="team_website" class="form-input"
                                   value="<?php echo esc_url($team->website_url); ?>"
                                   placeholder="https://example.com">
                        </div>

                        <div class="form-group">
                            <label for="team_facebook" class="form-label">
                                <span class="dashicons dashicons-facebook"></span>
                                فيسبوك
                            </label>
                            <input type="url" name="team_facebook" id="team_facebook" class="form-input"
                                   value="<?php echo esc_url($team->facebook_url); ?>"
                                   placeholder="https://facebook.com/yourteam">
                        </div>

                        <div class="form-group">
                            <label for="team_twitter" class="form-label">
                                <span class="dashicons dashicons-twitter"></span>
                                تويتر
                            </label>
                            <input type="url" name="team_twitter" id="team_twitter" class="form-input"
                                   value="<?php echo esc_url($team->twitter_url); ?>"
                                   placeholder="https://twitter.com/yourteam">
                        </div>

                        <div class="form-group">
                            <label for="team_discord" class="form-label">
                                <span class="dashicons dashicons-format-chat"></span>
                                ديسكورد
                            </label>
                            <input type="url" name="team_discord" id="team_discord" class="form-input"
                                   value="<?php echo esc_url($team->discord_url); ?>"
                                   placeholder="https://discord.gg/yourserver">
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-header">
                        <h3>
                            <span class="dashicons dashicons-privacy"></span>
                            إعدادات الخصوصية
                        </h3>
                        <p>تحكم في من يمكنه رؤية فريقك ومعلوماته</p>
                    </div>

                    <div class="privacy-options">
                        <div class="privacy-option">
                            <input type="radio" name="team_privacy" id="privacy_public" value="public"
                                   <?php checked($team->privacy, 'public'); ?>>
                            <label for="privacy_public" class="privacy-label">
                                <div class="privacy-icon">
                                    <span class="dashicons dashicons-visibility"></span>
                                </div>
                                <div class="privacy-content">
                                    <h4>عام</h4>
                                    <p>يمكن للجميع رؤية الفريق وأعضائه ومعلوماته</p>
                                </div>
                            </label>
                        </div>

                        <div class="privacy-option">
                            <input type="radio" name="team_privacy" id="privacy_private" value="private"
                                   <?php checked($team->privacy, 'private'); ?>>
                            <label for="privacy_private" class="privacy-label">
                                <div class="privacy-icon">
                                    <span class="dashicons dashicons-lock"></span>
                                </div>
                                <div class="privacy-content">
                                    <h4>خاص</h4>
                                    <p>يمكن للجميع رؤية الفريق ولكن الأعضاء فقط يمكنهم رؤية قائمة الأعضاء</p>
                                </div>
                            </label>
                        </div>

                        <div class="privacy-option">
                            <input type="radio" name="team_privacy" id="privacy_hidden" value="hidden"
                                   <?php checked($team->privacy, 'hidden'); ?>>
                            <label for="privacy_hidden" class="privacy-label">
                                <div class="privacy-icon">
                                    <span class="dashicons dashicons-hidden"></span>
                                </div>
                                <div class="privacy-content">
                                    <h4>مخفي</h4>
                                    <p>لا يمكن لأحد رؤية الفريق سوى الأعضاء المنضمين إليه</p>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Submit Section -->
            <div class="form-submit-section">
                <div class="submit-container">
                    <div class="submit-actions">
                        <button type="submit" name="submit" class="btn btn-primary btn-large">
                            <span class="dashicons dashicons-yes"></span>
                            حفظ جميع التغييرات
                        </button>
                        <a href="<?php echo admin_url('admin.php?page=team-system'); ?>" class="btn btn-secondary">
                            <span class="dashicons dashicons-arrow-left-alt"></span>
                            العودة للقائمة
                        </a>
                    </div>
                    <div class="submit-help">
                        <span class="dashicons dashicons-info"></span>
                        تأكد من حفظ التغييرات قبل مغادرة الصفحة
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Define AJAX variables if not already defined
if (typeof ajaxurl === 'undefined') {
    var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
}

// Define team system AJAX object if not already defined
if (typeof teamSystemAjax === 'undefined') {
    var teamSystemAjax = {
        nonce: '<?php echo wp_create_nonce('team_system_nonce'); ?>',
        ajaxurl: ajaxurl
    };
}

jQuery(document).ready(function($) {
    // Hide WordPress footer
    $('#wpfooter').hide();

    // Modern Tab switching
    $('.tab-btn').on('click', function(e) {
        e.preventDefault();
        var target = $(this).data('tab');

        // Update active tab
        $('.tab-btn').removeClass('active');
        $(this).addClass('active');

        // Show target tab content with smooth transition
        $('.tab-content').removeClass('active').hide();
        $('#' + target).addClass('active').fadeIn(300);

        // Scroll to top of content
        $('.modern-form-container').animate({
            scrollTop: 0
        }, 300);
    });

    // Ensure team-members tab shows all content
    $('#team-members').on('show', function() {
        $(this).find('.form-section').show();
    });

    // Force show all sections in team-members tab when it becomes active
    $('.tab-btn[data-tab="team-members"]').on('click', function() {
        setTimeout(function() {
            $('#team-members .form-section').show();
        }, 100);
    });

    // Initialize: make sure first tab is active and visible
    if ($('.tab-btn.active').length === 0) {
        $('.tab-btn').first().addClass('active');
        $('.tab-content').first().addClass('active').show();
    }
    
    // Add member
    $('#add_member_btn').on('click', function() {
        var username = $('#new_member_username').val().trim();
        var role = $('#new_member_role').val();

        if (!username) {
            alert('الرجاء إدخال اسم المستخدم أو البريد الإلكتروني');
            return;
        }

        // Send AJAX request to add member
        $.post(ajaxurl, {
            action: 'team_system_add_member',
            team_id: <?php echo $team_id; ?>,
            username: username,
            role: role,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                // Show success message
                alert(response.data.message || 'تم إرسال الدعوة بنجاح');
                // Clear form
                $('#new_member_username').val('');
                // Reload to show pending invitation
                location.reload();
            } else {
                alert(response.data.message || 'حدث خطأ ما');
            }
        }).fail(function() {
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });
    
    // Remove member
    $('.remove-member').on('click', function() {
        if (!confirm('هل أنت متأكد من إزالة هذا العضو من الفريق؟')) {
            return;
        }
        
        var $row = $(this).closest('.team-member-row');
        var userId = $(this).data('user-id');
        
        // Send AJAX request to remove member
        $.post(ajaxurl, {
            action: 'team_system_remove_member',
            team_id: <?php echo $team_id; ?>,
            user_id: userId,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                $row.fadeOut(300, function() {
                    $(this).remove();
                });
            } else {
                alert(response.data.message || 'حدث خطأ ما');
            }
        }).fail(function() {
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });

    // Cancel invitation
    $('.cancel-invitation').on('click', function() {
        if (!confirm('هل أنت متأكد من إلغاء هذه الدعوة؟')) {
            return;
        }

        var $row = $(this).closest('.invitation-row');
        var invitationId = $(this).data('invitation-id');

        // Send AJAX request to cancel invitation
        $.post(ajaxurl, {
            action: 'team_system_cancel_invitation',
            invitation_id: invitationId,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                $row.fadeOut(300, function() {
                    $(this).remove();
                });
                alert(response.data.message || 'تم إلغاء الدعوة');
            } else {
                alert(response.data.message || 'حدث خطأ ما');
            }
        }).fail(function() {
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });

    // Team slug validation and availability check for edit form
    var slugCheckTimeout;
    var currentSlug = $('#team_slug').val();

    $('#team_slug').on('input', function() {
        var $input = $(this);
        var slug = $input.val().toLowerCase();
        var $container = $('#slug-container');
        var $statusMessage = $('#slug-status-message');
        var $loading = $('#slug-loading');
        var $preview = $('#slug-preview');
        var $previewLink = $('#preview-link');
        var currentTeamId = $input.data('current-team-id');

        // Force lowercase
        $input.val(slug);

        // Clear previous timeout
        clearTimeout(slugCheckTimeout);

        // Reset states
        $container.removeClass('checking available unavailable error');
        $statusMessage.removeClass('show available unavailable error checking');
        $loading.removeClass('show');
        $preview.removeClass('show');

        // If empty, reset to default state
        if (!slug) {
            return;
        }

        // Basic format validation
        if (!/^[a-z0-9\-]+$/.test(slug)) {
            $container.addClass('error');
            $statusMessage.addClass('show error');
            $statusMessage.find('.status-text').text('يُسمح بالأحرف الإنجليزية الصغيرة والأرقام والشرطة فقط');
            return;
        }

        // Check minimum length
        if (slug.length < 3) {
            $container.addClass('error');
            $statusMessage.addClass('show error');
            $statusMessage.find('.status-text').text('يجب أن يكون الرابط 3 أحرف على الأقل');
            return;
        }

        // If same as current slug, show as available
        if (slug === currentSlug) {
            $container.addClass('available');
            $statusMessage.addClass('show available');
            $statusMessage.find('.status-text').text('هذا هو الرابط الحالي لفريقك');
            return;
        }

        // Show loading state
        $container.addClass('checking');
        $statusMessage.addClass('show checking');
        $statusMessage.find('.status-text').text('جاري التحقق من توفر الرابط...');
        $loading.addClass('show');

        // Update preview
        var newUrl = '<?php echo home_url('/teams/'); ?>' + slug;
        $previewLink.attr('href', newUrl).text(newUrl);
        $preview.addClass('show');

        // Debounced AJAX check
        slugCheckTimeout = setTimeout(function() {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'check_team_slug_availability',
                    slug: slug,
                    current_team_id: currentTeamId,
                    nonce: '<?php echo wp_create_nonce('check_slug_nonce'); ?>'
                },
                success: function(response) {
                    $loading.removeClass('show');
                    $container.removeClass('checking');
                    $statusMessage.removeClass('checking');

                    if (response.success && response.data) {
                        if (response.data.available) {
                            $container.addClass('available');
                            $statusMessage.addClass('show available');
                            $statusMessage.find('.status-text').text(response.data.message);
                        } else {
                            $container.addClass('unavailable');
                            $statusMessage.addClass('show unavailable');
                            $statusMessage.find('.status-text').text(response.data.message);
                        }
                    } else {
                        $container.addClass('error');
                        $statusMessage.addClass('show error');
                        $statusMessage.find('.status-text').text('حدث خطأ أثناء التحقق من الرابط');
                    }
                },
                error: function() {
                    $loading.removeClass('show');
                    $container.removeClass('checking').addClass('error');
                    $statusMessage.removeClass('checking').addClass('show error');
                    $statusMessage.find('.status-text').text('خطأ في الاتصال بالخادم');
                }
            });
        }, 500); // Wait 500ms after user stops typing
    });

    // Add helpful tooltips and interactions
    $('#team_slug').on('focus', function() {
        var $container = $('#slug-container');
        $container.addClass('focused');

        // Show helpful hint if empty
        if (!$(this).val()) {
            var $hint = $('<div class="slug-hint">اكتب رابط فريقك باللغة الإنجليزية</div>');
            $container.append($hint);
            setTimeout(function() {
                $hint.fadeOut(function() {
                    $hint.remove();
                });
            }, 3000);
        }
    }).on('blur', function() {
        $('#slug-container').removeClass('focused');
    });

    // Prevent invalid characters
    $('#team_slug').on('keypress', function(e) {
        var char = String.fromCharCode(e.which);
        if (!/[a-z0-9\-]/.test(char) && e.which !== 8 && e.which !== 0) {
            e.preventDefault();

            // Show brief error indication
            var $container = $('#slug-container');
            var $statusMessage = $('#slug-status-message');

            $container.addClass('invalid-char');
            $statusMessage.addClass('show error');
            $statusMessage.find('.status-text').text('هذا الحرف غير مسموح');

            setTimeout(function() {
                $container.removeClass('invalid-char');
                $statusMessage.removeClass('show error');
                $statusMessage.find('.status-text').text('');
            }, 1500);
        }
    });

    // Auto-suggest based on team name
    $('#team_name').on('input', function() {
        var teamName = $(this).val();
        var $slugInput = $('#team_slug');

        // Only auto-suggest if slug is empty or matches previous suggestion
        if (!$slugInput.val() || $slugInput.data('auto-suggested')) {
            var suggestedSlug = teamName
                .toLowerCase()
                .replace(/[^a-z0-9\s\-]/g, '') // Remove invalid chars
                .replace(/\s+/g, '-') // Replace spaces with hyphens
                .replace(/\-+/g, '-') // Replace multiple hyphens with single
                .replace(/^\-|\-$/g, ''); // Remove leading/trailing hyphens

            if (suggestedSlug && suggestedSlug !== $slugInput.val()) {
                $slugInput.val(suggestedSlug).trigger('input');
                $slugInput.data('auto-suggested', true);
            }
        }
    });

    // Clear auto-suggestion flag when user manually edits slug
    $('#team_slug').on('keydown', function() {
        $(this).data('auto-suggested', false);
    });

    // File input preview
    $('#team_logo').on('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                var $preview = $('.logo-preview');
                if ($preview.length) {
                    $preview.attr('src', e.target.result);
                } else {
                    $('.no-image-placeholder').first().replaceWith(
                        '<img src="' + e.target.result + '" class="preview-image logo-preview" alt="شعار الفريق">'
                    );
                }
            };
            reader.readAsDataURL(file);
        }
    });

    $('#team_cover').on('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                var $preview = $('.cover-preview');
                if ($preview.length) {
                    $preview.attr('src', e.target.result);
                } else {
                    $('.cover-placeholder').replaceWith(
                        '<img src="' + e.target.result + '" class="preview-image cover-preview" alt="صورة الغلاف">'
                    );
                }
            };
            reader.readAsDataURL(file);
        }
    });

    // Add loading states
    $('.btn').on('click', function() {
        if ($(this).hasClass('remove-member') || $(this).hasClass('cancel-invitation')) {
            return; // Don't add loading to these buttons
        }

        var $btn = $(this);
        var originalText = $btn.html();

        $btn.addClass('loading').html('<span class="dashicons dashicons-update" style="animation: spin 1s linear infinite;"></span> جاري المعالجة...');

        // Remove loading state after form submission or action
        setTimeout(function() {
            $btn.removeClass('loading').html(originalText);
        }, 2000);
    });

    // Add ripple effect to buttons
    $('.btn').on('click', function(e) {
        var $button = $(this);
        var $ripple = $('<span class="ripple"></span>');

        var buttonOffset = $button.offset();
        var xPos = e.pageX - buttonOffset.left;
        var yPos = e.pageY - buttonOffset.top;

        $ripple.css({
            'left': xPos + 'px',
            'top': yPos + 'px',
            'position': 'absolute',
            'border-radius': '50%',
            'background': 'rgba(255, 255, 255, 0.6)',
            'transform': 'scale(0)',
            'animation': 'ripple-animation 0.6s linear',
            'pointer-events': 'none'
        });

        $button.append($ripple);

        setTimeout(function() {
            $ripple.remove();
        }, 600);
    });

    // Auto-save indication
    var formChanged = false;
    $('.team-edit-form input, .team-edit-form select, .team-edit-form textarea').on('change', function() {
        formChanged = true;
        $('.submit-help').html('<span class="dashicons dashicons-warning"></span> يوجد تغييرات غير محفوظة');
    });

    // Warn before leaving if changes not saved
    $(window).on('beforeunload', function() {
        if (formChanged) {
            return 'لديك تغييرات غير محفوظة. هل أنت متأكد من المغادرة؟';
        }
    });

    // Remove warning when form is submitted
    $('.team-edit-form').on('submit', function() {
        formChanged = false;
    });

    // Add smooth animations for member cards
    $('.member-card').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateY(20px)'
        }).delay(index * 100).animate({
            'opacity': '1'
        }, 500).css('transform', 'translateY(0)');
    });

    // Add smooth animations for invitation cards
    $('.invitation-card').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateY(20px)'
        }).delay(index * 100).animate({
            'opacity': '1'
        }, 500).css('transform', 'translateY(0)');
    });

    // Enhanced form validation
    $('.form-input[required]').on('blur', function() {
        var $input = $(this);
        var value = $input.val().trim();

        if (!value) {
            $input.css('border-color', '#dc3545');
            if (!$input.next('.error-message').length) {
                $input.after('<div class="error-message" style="color: #dc3545; font-size: 12px; margin-top: 4px;">هذا الحقل مطلوب</div>');
            }
        } else {
            $input.css('border-color', '#10b981');
            $input.next('.error-message').remove();
        }
    });

    // Add CSS for animations
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
            .slug-input-container.error {
                border-color: #dc3545 !important;
                box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
            }

            /* Pulse animation for checking state */
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }

            .slug-input-container.checking {
                animation: pulse 1.5s ease-in-out infinite;
            }

            /* Slide in animation for status */
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(10px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            .slug-status.show {
                animation: slideInRight 0.3s ease-out;
            }

            /* Bounce animation for available status */
            @keyframes bounce {
                0%, 20%, 53%, 80%, 100% {
                    transform: translateY(0);
                }
                40%, 43% {
                    transform: translateY(-3px);
                }
                70% {
                    transform: translateY(-2px);
                }
                90% {
                    transform: translateY(-1px);
                }
            }

            .slug-status-message.available .status-icon {
                animation: bounce 0.6s ease-out;
            }

            /* Slide in animation for status message */
            @keyframes slideInDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .slug-status-message.show {
                animation: slideInDown 0.3s ease-out;
            }

            /* Shake animation for error */
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
                20%, 40%, 60%, 80% { transform: translateX(2px); }
            }

            .slug-input-container.error {
                animation: shake 0.5s ease-in-out;
            }

            /* Glow effect for available state */
            .slug-input-container.available {
                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1), 0 0 20px rgba(16, 185, 129, 0.1) !important;
            }

            /* Enhanced preview styling */
            .slug-preview {
                transform: translateY(-5px);
                opacity: 0;
                transition: all 0.3s ease-out;
            }

            .slug-preview.show {
                transform: translateY(0);
                opacity: 1;
            }
        `)
        .appendTo('head');
});
</script>

<style>
/* Modern Team Edit Page Styling */
.team-edit-wrap {
    background: #f8f9fa;
    margin: 0 -20px;
    padding: 0;
}

/* Hide WordPress footer in team edit page */
#wpfooter {
    display: none !important;
}

/* Fix admin footer positioning */
.wrap.team-edit-wrap ~ #wpfooter,
.wrap.team-edit-wrap + #wpfooter {
    display: none !important;
}

/* Ensure proper page layout */
.team-edit-wrap {
    min-height: 100vh;
    position: relative;
    z-index: 1;
}

/* Header Section */
.team-edit-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    margin: 0 0 30px 0;
    position: relative;
    overflow: hidden;
}

.team-edit-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
}

.team-header-info {
    display: flex;
    align-items: center;
    gap: 25px;
}

.team-logo-display {
    flex-shrink: 0;
}

.team-logo-display img {
    width: 80px;
    height: 80px;
    border-radius: 16px;
    object-fit: cover;
    box-shadow: 0 8px 24px rgba(0,0,0,0.2);
    border: 3px solid rgba(255,255,255,0.2);
}

.team-logo-placeholder {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255,255,255,0.3);
}

.team-logo-placeholder .dashicons {
    font-size: 40px;
    width: 40px;
    height: 40px;
    color: white;
}

.team-header-text h1 {
    margin: 0 0 10px 0;
    font-size: 32px;
    font-weight: 700;
    color: white;
    display: flex;
    align-items: center;
    gap: 15px;
}

.team-header-text h1 .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
}

.team-edit-subtitle {
    margin: 0;
    font-size: 16px;
    color: rgba(255,255,255,0.9);
    line-height: 1.5;
}

.header-actions {
    display: flex;
    gap: 15px;
}

/* Modern Tab Navigation */
.modern-tabs {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin: 0 40px 30px 40px;
    overflow: hidden;
}

.tab-navigation {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 20px 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    color: #6c757d;
    font-size: 14px;
    font-weight: 600;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: white;
    color: #667eea;
    box-shadow: 0 -2px 0 #667eea inset;
}

.tab-btn .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.tab-label {
    font-weight: 600;
}

.member-count {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
    min-width: 20px;
    text-align: center;
}

/* Modern Form Container */
.modern-form-container {
    margin: 0 40px;
}

.tab-content {
    display: none;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.tab-content.active {
    display: block !important;
}

/* Ensure all sections in team-members tab are visible */
#team-members .form-section {
    display: block !important;
}

#team-members.active .form-section {
    display: block !important;
}

/* Form Sections */
.form-section {
    padding: 40px;
    border-bottom: 1px solid #f0f0f1;
}

.form-section:last-child {
    border-bottom: none;
}

.section-header {
    margin-bottom: 30px;
    text-align: center;
}

.section-header h3 {
    margin: 0 0 10px 0;
    font-size: 24px;
    font-weight: 700;
    color: #1d2327;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.section-header h3 .dashicons {
    font-size: 28px;
    width: 28px;
    height: 28px;
    color: #667eea;
}

.section-header p {
    margin: 0;
    color: #6c757d;
    font-size: 16px;
    line-height: 1.5;
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.form-label .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #667eea;
}

.form-input,
.form-select {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-help {
    margin-top: 8px;
    font-size: 13px;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 6px;
}

.form-help .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    color: #9ca3af;
}

.form-help a {
    color: #667eea;
    text-decoration: none;
}

.form-help a:hover {
    text-decoration: underline;
}

/* Slug Input with Availability Check */
.slug-input-container {
    display: flex;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.slug-input-container:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.slug-input-container.checking {
    border-color: #f59e0b;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.slug-input-container.available {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.slug-input-container.unavailable {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.slug-input-container.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.slug-prefix {
    background: #f9fafb;
    padding: 12px 16px;
    font-size: 14px;
    color: #6b7280;
    border-right: 1px solid #e5e7eb;
    white-space: nowrap;
    display: flex;
    align-items: center;
}

.slug-input {
    border: none !important;
    box-shadow: none !important;
    flex: 1;
    padding: 12px 16px;
    padding-right: 50px; /* Space for loading indicator only */
}

.slug-input:focus {
    outline: none;
    box-shadow: none;
}

/* Status Message Outside Input */
.slug-status-message {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 600;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.3s ease;
    min-height: 20px;
}

.slug-status-message.show {
    opacity: 1;
    transform: translateY(0);
}

.slug-status-message.available {
    color: #10b981;
}

.slug-status-message.unavailable {
    color: #ef4444;
}

.slug-status-message.error {
    color: #ef4444;
}

.slug-status-message.checking {
    color: #f59e0b;
}

/* Status Message Icons */
.slug-status-message .status-icon {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    color: white;
    font-weight: bold;
    flex-shrink: 0;
}

.slug-status-message.available .status-icon {
    background: #10b981;
}

.slug-status-message.available .status-icon::before {
    content: "✓";
}

.slug-status-message.unavailable .status-icon {
    background: #ef4444;
}

.slug-status-message.unavailable .status-icon::before {
    content: "✗";
}

.slug-status-message.error .status-icon {
    background: #ef4444;
}

.slug-status-message.error .status-icon::before {
    content: "!";
}

.slug-status-message.checking .status-icon {
    background: #f59e0b;
    animation: pulse 1.5s ease-in-out infinite;
}

.slug-status-message.checking .status-icon::before {
    content: "⟳";
    animation: spin 1s linear infinite;
}

.slug-loading {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    display: none;
    color: #f59e0b;
}

.slug-loading.show {
    display: block;
}

.slug-loading .dashicons {
    animation: spin 1s linear infinite;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.slug-preview {
    margin-top: 8px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #0ea5e9;
    border-radius: 8px;
    display: none;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #0369a1;
}

.slug-preview.show {
    display: flex;
}

.slug-preview .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #0ea5e9;
}

.slug-preview a {
    color: #0369a1;
    text-decoration: none;
    font-weight: 600;
}

.slug-preview a:hover {
    text-decoration: underline;
}

/* Status Messages */
.slug-message {
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    display: none;
    align-items: center;
    gap: 8px;
}

.slug-message.show {
    display: flex;
}

.slug-message.available {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.slug-message.unavailable {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.slug-message.error {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.slug-message .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Additional Slug Input Enhancements */
.slug-input-container.focused {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15) !important;
}

.slug-input-container.invalid-char {
    border-color: #ef4444 !important;
    animation: shake 0.3s ease-in-out !important;
}

.slug-hint {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #1f2937;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    z-index: 1000;
    margin-top: 4px;
    opacity: 0;
    animation: fadeInUp 0.3s ease-out forwards;
}

.slug-hint::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #1f2937;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced status indicators */
.slug-status .status-text {
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
}

/* Improved loading animation */
.slug-loading .dashicons {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Success state enhancement */
.slug-input-container.available .slug-input {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

/* Error state enhancement */
.slug-input-container.error .slug-input,
.slug-input-container.unavailable .slug-input {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

/* Checking state enhancement */
.slug-input-container.checking .slug-input {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

/* Responsive adjustments for slug input */
@media (max-width: 768px) {
    .slug-input-container {
        flex-direction: column;
    }

    .slug-prefix {
        border-right: none;
        border-bottom: 1px solid #e5e7eb;
        border-radius: 8px 8px 0 0;
    }

    .slug-input {
        border-radius: 0 0 8px 8px;
        padding-right: 50px;
    }

    .slug-loading {
        right: 12px;
        top: auto;
        bottom: 12px;
        transform: none;
    }

    .slug-status-message {
        font-size: 12px;
        margin-bottom: 6px;
    }

    .slug-status-message .status-icon {
        width: 16px;
        height: 16px;
        font-size: 10px;
    }
}

/* Image Upload */
.image-upload-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.current-image {
    display: flex;
    justify-content: center;
}

.preview-image {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.logo-preview {
    width: 120px;
    height: 120px;
    object-fit: cover;
}

.cover-preview {
    max-width: 100%;
    height: auto;
    max-height: 200px;
    object-fit: cover;
}

.no-image-placeholder {
    width: 120px;
    height: 120px;
    background: #f3f4f6;
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #9ca3af;
    font-size: 13px;
}

.cover-placeholder {
    width: 100%;
    height: 150px;
}

.no-image-placeholder .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
}

.upload-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.file-input {
    display: none;
}

.upload-help {
    font-size: 12px;
    color: #6b7280;
    text-align: center;
}

/* Editor Container */
.editor-container {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.editor-container:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover:before {
    left: 100%;
}

.btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 2px solid #e9ecef;
}

.btn-secondary:hover {
    background: #e9ecef;
    color: #212529;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn-outline:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4);
    color: white;
}

.btn-large {
    padding: 16px 28px;
    font-size: 16px;
}

.btn-large .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

/* Members Grid */
.members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 25px;
}

.member-card {
    background: white;
    border: 2px solid #f0f0f1;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.member-card:hover {
    border-color: #667eea;
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.member-card-header {
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
}

.member-avatar-large {
    position: relative;
    flex-shrink: 0;
}

.member-avatar-large img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.leader-crown {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
    border: 2px solid white;
}

.leader-crown .dashicons {
    color: #b45309;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.member-info {
    flex: 1;
    min-width: 0;
}

.member-name {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 700;
    color: #1f2937;
}

.member-email {
    margin: 0 0 12px 0;
    color: #6b7280;
    font-size: 14px;
}

.member-meta {
    display: flex;
    align-items: center;
    gap: 8px;
}

.join-date {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #9ca3af;
}

.join-date .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.member-card-body {
    padding: 20px 25px;
}

.member-role-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.role-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.role-label .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #667eea;
}

.leader-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #b45309;
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 700;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.leader-badge .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.role-select {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: all 0.3s ease;
}

.role-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.member-card-footer {
    padding: 20px 25px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: center;
}

.leader-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 14px;
    font-style: italic;
}

.leader-info .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Invitations Grid */
.invitations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
}

.invitation-card {
    background: white;
    border: 2px solid #fef3c7;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.invitation-card:hover {
    border-color: #f59e0b;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.15);
}

.invitation-header {
    padding: 20px;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    display: flex;
    align-items: center;
    gap: 15px;
}

.invitation-avatar {
    position: relative;
    flex-shrink: 0;
}

.invitation-avatar img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.pending-indicator {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 24px;
    height: 24px;
    background: #f59e0b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
}

.pending-indicator .dashicons {
    color: white;
    font-size: 12px;
    width: 12px;
    height: 12px;
}

.invitation-info {
    flex: 1;
    min-width: 0;
}

.invitation-name {
    margin: 0 0 6px 0;
    font-size: 16px;
    font-weight: 700;
    color: #92400e;
}

.invitation-email {
    margin: 0;
    color: #b45309;
    font-size: 14px;
}

.invitation-body {
    padding: 20px;
}

.invitation-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #6b7280;
}

.detail-item .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    color: #f59e0b;
}

.invitation-status {
    display: flex;
    justify-content: center;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #fef3c7;
    color: #92400e;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid #fde68a;
}

.status-badge .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
}

.invitation-footer {
    padding: 15px 20px;
    background: #fefbf3;
    border-top: 1px solid #fde68a;
    display: flex;
    justify-content: center;
}

/* Add Member Container */
.add-member-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: start;
}

.add-member-form {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
    border: 2px solid #e9ecef;
}

.add-member-help {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.help-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #e7f3ff;
    border-radius: 8px;
    border-left: 4px solid #0073aa;
}

.help-item .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
    color: #0073aa;
    flex-shrink: 0;
}

.help-item span {
    font-size: 14px;
    color: #1e40af;
    line-height: 1.5;
}

/* Privacy Options */
.privacy-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.privacy-option {
    position: relative;
}

.privacy-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.privacy-label {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.privacy-label:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.privacy-option input[type="radio"]:checked + .privacy-label {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #e7f3ff 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.privacy-icon {
    width: 50px;
    height: 50px;
    background: #f3f4f6;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.privacy-option input[type="radio"]:checked + .privacy-label .privacy-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.privacy-icon .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: #9ca3af;
    transition: all 0.3s ease;
}

.privacy-option input[type="radio"]:checked + .privacy-label .privacy-icon .dashicons {
    color: white;
}

.privacy-content h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 700;
    color: #1f2937;
}

.privacy-content p {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
}

/* Submit Section */
.form-submit-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin: 30px 40px 40px 40px;
    overflow: hidden;
}

.submit-container {
    padding: 30px 40px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.submit-actions {
    display: flex;
    gap: 15px;
}

.submit-help {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 14px;
}

.submit-help .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .team-header-info {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .team-edit-header {
        padding: 30px 20px;
    }

    .modern-tabs,
    .modern-form-container,
    .form-submit-section {
        margin-left: 20px;
        margin-right: 20px;
    }

    .tab-navigation {
        flex-direction: column;
    }

    .form-section {
        padding: 30px 20px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .members-grid,
    .invitations-grid {
        grid-template-columns: 1fr;
    }

    .add-member-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .submit-container {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .submit-actions {
        flex-direction: column;
        width: 100%;
    }

    .submit-actions .btn {
        justify-content: center;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Focus States */
.btn:focus-visible {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Fix WordPress admin layout conflicts */
.team-edit-wrap {
    clear: both;
    overflow: hidden;
}

.team-edit-wrap * {
    box-sizing: border-box;
}

/* Prevent footer overlap */
#wpcontent {
    padding-bottom: 0 !important;
}

#wpbody-content {
    padding-bottom: 0 !important;
}

/* Fix any floating elements */
.team-edit-wrap::after {
    content: "";
    display: table;
    clear: both;
}

/* Ensure proper stacking */
.modern-form-container {
    position: relative;
    z-index: 10;
}

/* Fix tab content positioning */
.tab-content {
    position: relative;
    z-index: 5;
}

/* Print Styles */
@media print {
    .team-edit-header,
    .tab-navigation,
    .submit-container,
    .btn {
        display: none !important;
    }

    .tab-content {
        display: block !important;
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }

    #wpfooter {
        display: none !important;
    }
}

/* Pending Invitations Styling */
.pending-invitations-list {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.invitation-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.invitation-row:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.invitation-info {
    flex: 1;
}

.invitation-user {
    margin-bottom: 8px;
}

.invitation-user strong {
    color: #2c3e50;
    font-size: 16px;
}

.invitation-email {
    color: #7f8c8d;
    font-size: 14px;
    margin-left: 10px;
}

.invitation-details {
    display: flex;
    gap: 15px;
    font-size: 13px;
    color: #6c757d;
}

.invitation-role {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.invitation-status {
    margin: 0 15px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.invitation-actions .button {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.invitation-actions .button:hover {
    background: #c82333;
}

/* Add member section styling */
.add-member-section {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.add-member-section input[type="email"] {
    flex: 2;
}

.add-member-section select {
    flex: 1;
}

.add-member-section .button {
    flex: 0 0 auto;
}
</style>

<style>


.team-member-row {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.team-member-row:last-child {
    border-bottom: none;
}

.member-avatar {
    margin-left: 15px;
}

.member-avatar img {
    border-radius: 50%;
}

.member-info {
    flex: 1;
}

.member-name {
    font-weight: 600;
    margin: 0 0 3px 0;
}

.member-email {
    color: #646970;
    font-size: 0.9em;
}

.member-role {
    min-width: 150px;
    margin: 0 15px;
}

.member-role select {
    width: 100%;
}

.team-leader-badge {
    background: #f0f0f1;
    color: #1d2327;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 0.85em;
}

.add-member-section {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.add-member-section input[type="email"] {
    flex: 1;
    max-width: 300px;
}

.submit-buttons {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}
</style>