<div class="wrap">
    <h1 class="wp-heading-inline">إدارة الفرق</h1>
    <a href="<?php echo admin_url('admin.php?page=team-system-add'); ?>" class="page-title-action">إضافة فريق جديد</a>
    <hr class="wp-header-end">

    <?php
    // Show team invitations for current user
    global $wpdb;
    $current_user_id = get_current_user_id();
    $pending_invitations = $wpdb->get_results($wpdb->prepare(
        "SELECT i.*, t.name as team_name, t.slug as team_slug, u.display_name as invited_by_name
         FROM {$wpdb->prefix}team_invitations i
         JOIN {$wpdb->prefix}teams t ON i.team_id = t.id
         JOIN {$wpdb->users} u ON i.invited_by = u.ID
         WHERE i.user_id = %d AND i.status = 'pending' AND i.expires_at > NOW()
         ORDER BY i.created_at DESC",
        $current_user_id
    ));

    if (!empty($pending_invitations)): ?>
        <div class="team-invitations-section">
            <h2>دعوات الانضمام للفرق</h2>
            <div class="invitations-grid">
                <?php foreach ($pending_invitations as $invitation): ?>
                    <div class="invitation-card" data-invitation-id="<?php echo $invitation->id; ?>">
                        <div class="invitation-header">
                            <h3>دعوة للانضمام إلى فريق</h3>
                            <span class="invitation-date"><?php echo date_i18n('j F Y', strtotime($invitation->created_at)); ?></span>
                        </div>

                        <div class="invitation-body">
                            <div class="team-info">
                                <h4 class="team-name"><?php echo esc_html($invitation->team_name); ?></h4>
                                <p class="invitation-text">
                                    دعاك <strong><?php echo esc_html($invitation->invited_by_name); ?></strong>
                                    للانضمام إلى الفريق بدور <strong><?php echo esc_html($this->get_role_name($invitation->role)); ?></strong>
                                </p>
                            </div>

                            <div class="invitation-expires">
                                <small>تنتهي صلاحية الدعوة في: <?php echo date_i18n('j F Y', strtotime($invitation->expires_at)); ?></small>
                            </div>
                        </div>

                        <div class="invitation-actions">
                            <button type="button" class="button button-primary accept-invitation"
                                    data-invitation-id="<?php echo $invitation->id; ?>">
                                قبول الدعوة
                            </button>
                            <button type="button" class="button decline-invitation"
                                    data-invitation-id="<?php echo $invitation->id; ?>">
                                رفض الدعوة
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Quick Team Creation Section -->
    <div class="quick-team-creation">
        <div class="quick-create-content">
            <div class="quick-create-icon">
                <span class="dashicons dashicons-plus-alt"></span>
            </div>
            <div class="quick-create-text">
                <h2>إنشاء فريق جديد</h2>
                <p>ابدأ رحلتك في إدارة الفرق بإنشاء فريق جديد وإضافة الأعضاء المناسبين لمشروعك.</p>
            </div>
        </div>
        <div class="quick-create-buttons">
            <a href="<?php echo admin_url('admin.php?page=team-system-add'); ?>" class="btn btn-primary btn-large">
                <span class="dashicons dashicons-plus-alt"></span>
                إنشاء فريق جديد
            </a>
            <a href="#teams-section" class="btn btn-secondary scroll-to-teams">
                <span class="dashicons dashicons-arrow-down-alt"></span>
                عرض الفرق الحالية
            </a>
        </div>
    </div>
    
    <!-- Teams Grid Section -->
    <div class="teams-section" id="teams-section">
        <div class="teams-header">
            <h2>
                <span class="dashicons dashicons-groups"></span>
                فرقك المسجلة
            </h2>
            <div class="teams-stats">
                <?php
                global $wpdb;
                $teams = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}teams ORDER BY created_at DESC");
                $total_teams = count($teams);
                $total_members = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}team_members");
                ?>
                <div class="stat-item">
                    <span class="stat-number"><?php echo $total_teams; ?></span>
                    <span class="stat-label">فريق</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo $total_members; ?></span>
                    <span class="stat-label">عضو</span>
                </div>
            </div>
        </div>

        <?php if (!empty($teams)): ?>
            <div class="teams-grid">
                <?php foreach ($teams as $team):
                    $member_count = $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM {$wpdb->prefix}team_members WHERE team_id = %d",
                        $team->id
                    ));

                    // Get team members for avatars
                    $team_members = $wpdb->get_results($wpdb->prepare(
                        "SELECT u.ID, u.display_name, u.user_email
                         FROM {$wpdb->prefix}team_members tm
                         JOIN {$wpdb->users} u ON tm.user_id = u.ID
                         WHERE tm.team_id = %d
                         ORDER BY tm.joined_at ASC
                         LIMIT 4",
                        $team->id
                    ));
                    ?>
                    <div class="team-card" data-team-id="<?php echo $team->id; ?>">
                        <div class="team-card-header">
                            <div class="team-logo">
                                <?php if ($team->logo_url): ?>
                                    <img src="<?php echo esc_url($team->logo_url); ?>" alt="<?php echo esc_attr($team->name); ?>">
                                <?php else: ?>
                                    <div class="team-logo-placeholder">
                                        <span class="dashicons dashicons-groups"></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="team-info">
                                <h3 class="team-name"><?php echo esc_html($team->name); ?></h3>
                                <p class="team-description"><?php echo esc_html(wp_trim_words($team->description, 15)); ?></p>
                            </div>
                        </div>

                        <div class="team-card-body">
                            <div class="team-stats">
                                <div class="stat">
                                    <span class="dashicons dashicons-admin-users"></span>
                                    <span><?php echo intval($member_count); ?> عضو</span>
                                </div>
                                <div class="stat">
                                    <span class="dashicons dashicons-calendar-alt"></span>
                                    <span><?php echo date_i18n('j M Y', strtotime($team->created_at)); ?></span>
                                </div>
                            </div>

                            <?php if (!empty($team_members)): ?>
                                <div class="team-members-preview">
                                    <div class="members-avatars">
                                        <?php foreach ($team_members as $member): ?>
                                            <div class="member-avatar" title="<?php echo esc_attr($member->display_name); ?>">
                                                <?php echo get_avatar($member->user_email, 32); ?>
                                            </div>
                                        <?php endforeach; ?>
                                        <?php if ($member_count > 4): ?>
                                            <div class="member-avatar more-members">
                                                <span>+<?php echo ($member_count - 4); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="team-card-footer">
                            <div class="team-actions">
                                <a href="<?php echo admin_url('admin.php?page=team-system&action=edit&team_id=' . $team->id); ?>"
                                   class="btn btn-primary">
                                    <span class="dashicons dashicons-edit"></span>
                                    تعديل
                                </a>
                                <a href="<?php echo home_url('/teams/' . $team->slug); ?>"
                                   class="btn btn-secondary" target="_blank">
                                    <span class="dashicons dashicons-visibility"></span>
                                    عرض
                                </a>
                                <button class="btn btn-danger delete-team" data-team-id="<?php echo $team->id; ?>">
                                    <span class="dashicons dashicons-trash"></span>
                                    حذف
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="empty-state">
                <div class="empty-state-icon">
                    <span class="dashicons dashicons-groups"></span>
                </div>
                <h3>لا توجد فرق حتى الآن</h3>
                <p>ابدأ رحلتك في إدارة الفرق بإنشاء فريقك الأول</p>
                <a href="<?php echo admin_url('admin.php?page=team-system-add'); ?>" class="btn btn-primary btn-large">
                    <span class="dashicons dashicons-plus-alt"></span>
                    إنشاء فريق جديد
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Define AJAX variables if not already defined
if (typeof ajaxurl === 'undefined') {
    var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
}

// Define team system AJAX object if not already defined
if (typeof teamSystemAjax === 'undefined') {
    var teamSystemAjax = {
        nonce: '<?php echo wp_create_nonce('team_system_nonce'); ?>',
        ajaxurl: ajaxurl
    };
}

jQuery(document).ready(function($) {
    // Handle team deletion
    $('.delete-team').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm('هل أنت متأكد من حذف هذا الفريق؟ لا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }
        
        var $button = $(this);
        var teamId = $button.data('team-id');
        
        $.post(ajaxurl, {
            action: 'team_system_delete_team',
            team_id: teamId,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                $button.closest('tr').fadeOut(300, function() {
                    $(this).remove();
                });
            } else {
                alert(response.data.message || 'حدث خطأ أثناء حذف الفريق');
            }
        }).fail(function() {
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });

    // Accept invitation
    $('.accept-invitation').on('click', function() {
        var $card = $(this).closest('.invitation-card');
        var invitationId = $(this).data('invitation-id');

        $card.addClass('loading');

        $.post(ajaxurl, {
            action: 'team_system_accept_invitation',
            invitation_id: invitationId,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                $card.fadeOut(300, function() {
                    $(this).remove();
                    // Show success message
                    $('.team-invitations-section').prepend(
                        '<div class="invitation-message success">' +
                        (response.data.message || 'تم قبول الدعوة بنجاح!') +
                        '</div>'
                    );

                    // Check if no more invitations
                    if ($('.invitation-card').length === 0) {
                        $('.team-invitations-section').fadeOut(500);
                    }
                });
            } else {
                $card.removeClass('loading');
                alert(response.data.message || 'حدث خطأ أثناء قبول الدعوة');
            }
        }).fail(function() {
            $card.removeClass('loading');
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });

    // Decline invitation
    $('.decline-invitation').on('click', function() {
        if (!confirm('هل أنت متأكد من رفض هذه الدعوة؟')) {
            return;
        }

        var $card = $(this).closest('.invitation-card');
        var invitationId = $(this).data('invitation-id');

        $card.addClass('loading');

        $.post(ajaxurl, {
            action: 'team_system_decline_invitation',
            invitation_id: invitationId,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                $card.fadeOut(300, function() {
                    $(this).remove();

                    // Check if no more invitations
                    if ($('.invitation-card').length === 0) {
                        $('.team-invitations-section').fadeOut(500);
                    }
                });
            } else {
                $card.removeClass('loading');
                alert(response.data.message || 'حدث خطأ أثناء رفض الدعوة');
            }
        }).fail(function() {
            $card.removeClass('loading');
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });

    // Smooth scroll to teams section
    $('.scroll-to-teams').on('click', function(e) {
        e.preventDefault();
        $('html, body').animate({
            scrollTop: $('.teams-section').offset().top - 50
        }, 800, 'easeInOutCubic');
    });

    // Add loading animation to team cards when performing actions
    $('.team-card').on('click', '.btn', function() {
        if (!$(this).hasClass('delete-team')) {
            $(this).closest('.team-card').addClass('loading');
        }
    });

    // Add hover effects and animations
    $('.team-card').hover(
        function() {
            $(this).find('.team-logo img, .team-logo-placeholder').css('transform', 'scale(1.1)');
        },
        function() {
            $(this).find('.team-logo img, .team-logo-placeholder').css('transform', 'scale(1)');
        }
    );

    // Add entrance animations
    function animateCards() {
        $('.team-card').each(function(index) {
            $(this).css({
                'opacity': '0',
                'transform': 'translateY(30px)'
            }).delay(index * 100).animate({
                'opacity': '1'
            }, 600).css('transform', 'translateY(0)');
        });
    }

    // Trigger animations when page loads
    setTimeout(animateCards, 300);

    // Add ripple effect to buttons
    $('.btn').on('click', function(e) {
        var $button = $(this);
        var $ripple = $('<span class="ripple"></span>');

        var buttonOffset = $button.offset();
        var xPos = e.pageX - buttonOffset.left;
        var yPos = e.pageY - buttonOffset.top;

        $ripple.css({
            'left': xPos + 'px',
            'top': yPos + 'px'
        });

        $button.append($ripple);

        setTimeout(function() {
            $ripple.remove();
        }, 600);
    });
});
</script>

<style>
/* Team Invitations Styling */
.team-invitations-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.team-invitations-section h2 {
    margin-top: 0;
    color: #1d2327;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.invitations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.invitation-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 0;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.invitation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.invitation-header {
    background: rgba(255,255,255,0.1);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.invitation-header h3 {
    color: white;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.invitation-date {
    color: rgba(255,255,255,0.8);
    font-size: 12px;
}

.invitation-body {
    padding: 20px;
    color: white;
}

.team-name {
    color: white;
    font-size: 20px;
    font-weight: 700;
    margin: 0 0 10px 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.invitation-text {
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
    margin: 0 0 15px 0;
}

.invitation-expires {
    color: rgba(255,255,255,0.7);
    font-size: 12px;
}

.invitation-actions {
    padding: 15px 20px;
    background: rgba(255,255,255,0.1);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.invitation-actions .button {
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.invitation-actions .button-primary {
    background: #28a745;
    color: white;
}

.invitation-actions .button-primary:hover {
    background: #218838;
    transform: translateY(-1px);
}

.invitation-actions .decline-invitation {
    background: rgba(255,255,255,0.2);
    color: white;
}

.invitation-actions .decline-invitation:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

.invitation-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.invitation-message {
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
    font-weight: 500;
}

.invitation-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.invitation-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Modern Quick Team Creation Styling */
.quick-team-creation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 0;
    margin-top: 30px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    overflow: hidden;
    position: relative;
}

.quick-team-creation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.quick-create-content {
    display: flex;
    align-items: center;
    gap: 25px;
    padding: 40px;
    position: relative;
    z-index: 1;
}

.quick-create-icon {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.quick-create-icon .dashicons {
    font-size: 40px;
    width: 40px;
    height: 40px;
    color: white;
}

.quick-create-text {
    flex: 1;
    color: white;
}

.quick-create-text h2 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 700;
    color: white;
    border: none;
    padding: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quick-create-text p {
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.quick-create-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
    padding: 25px 40px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;
}

.scroll-to-teams {
    position: relative;
    overflow: hidden;
}

.scroll-to-teams::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 6px;
    height: 6px;
    border-right: 2px solid currentColor;
    border-bottom: 2px solid currentColor;
    transform: translateY(-50%) rotate(45deg);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(-50%) rotate(45deg) translateY(0);
    }
    40% {
        transform: translateY(-50%) rotate(45deg) translateY(-3px);
    }
    60% {
        transform: translateY(-50%) rotate(45deg) translateY(-1px);
    }
}

/* Admin Bar Notification Styling */
#wp-admin-bar-team-invitations .ab-item,
#wp-admin-bar-team-system .ab-item {
    color: #a7aaad !important;
}

#wp-admin-bar-team-invitations:hover .ab-item,
#wp-admin-bar-team-system:hover .ab-item {
    color: #00b9eb !important;
}

#wp-admin-bar-team-invitations .awaiting-mod {
    background: #d63638;
    color: #fff;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: 600;
    margin-left: 5px;
    min-width: 18px;
    text-align: center;
    display: inline-block;
}

#wp-admin-bar-team-invitations .ab-icon:before {
    content: "\f307";
    top: 2px;
}

#wp-admin-bar-team-system .ab-icon:before {
    content: "\f307";
    top: 2px;
}

/* Modern Teams Grid Styling */
.teams-section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    margin-top: 30px;
}

.teams-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 30px;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.teams-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    border: none;
    padding: 0;
}

.teams-header h2 .dashicons {
    font-size: 28px;
    width: 28px;
    height: 28px;
}

.teams-stats {
    display: flex;
    gap: 30px;
}

.stat-item {
    text-align: center;
    background: rgba(255,255,255,0.1);
    padding: 15px 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.stat-number {
    display: block;
    font-size: 28px;
    font-weight: 700;
    color: white;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: rgba(255,255,255,0.8);
    margin-top: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 25px;
    padding: 30px;
}

.team-card {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    border: 1px solid #f0f0f1;
    position: relative;
}

.team-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    border-color: #0073aa;
}

.team-card-header {
    padding: 25px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e9ecef;
}

.team-logo {
    flex-shrink: 0;
}

.team-logo img {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.team-logo-placeholder {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0,115,170,0.3);
}

.team-logo-placeholder .dashicons {
    color: white;
    font-size: 28px;
    width: 28px;
    height: 28px;
}

.team-info {
    flex: 1;
    min-width: 0;
}

.team-name {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 700;
    color: #1d2327;
    line-height: 1.3;
}

.team-description {
    margin: 0;
    color: #646970;
    font-size: 14px;
    line-height: 1.5;
}

.team-card-body {
    padding: 20px 25px;
}

.team-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.team-stats .stat {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #646970;
    font-size: 13px;
    font-weight: 500;
}

.team-stats .stat .dashicons {
    color: #0073aa;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.team-members-preview {
    margin-top: 15px;
}

.members-avatars {
    display: flex;
    gap: -8px;
}

.member-avatar {
    position: relative;
    margin-left: -8px;
    border: 3px solid #fff;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.member-avatar:first-child {
    margin-left: 0;
}

.member-avatar:hover {
    transform: translateY(-2px);
    z-index: 10;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.member-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.member-avatar.more-members {
    width: 32px;
    height: 32px;
    background: #0073aa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 11px;
    font-weight: 600;
}

.team-card-footer {
    padding: 20px 25px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.team-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover:before {
    left: 100%;
}

.btn .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(0,115,170,0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #005177 0%, #003d5c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,115,170,0.4);
    color: white;
}

.btn-secondary {
    background: #f6f7f7;
    color: #50575e;
    border: 1px solid #dcdcde;
}

.btn-secondary:hover {
    background: #f0f0f1;
    color: #1d2327;
    border-color: #8c8f94;
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
    box-shadow: 0 2px 8px rgba(220,53,69,0.3);
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220,53,69,0.4);
    color: white;
}

.btn-large {
    padding: 12px 24px;
    font-size: 16px;
}

.btn-large .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

/* Empty State Styling */
.empty-state {
    text-align: center;
    padding: 80px 30px;
    color: #646970;
}

.empty-state-icon {
    margin-bottom: 20px;
}

.empty-state-icon .dashicons {
    font-size: 80px;
    width: 80px;
    height: 80px;
    color: #c3c4c7;
}

.empty-state h3 {
    margin: 0 0 10px 0;
    font-size: 24px;
    font-weight: 600;
    color: #1d2327;
}

.empty-state p {
    margin: 0 0 30px 0;
    font-size: 16px;
    color: #646970;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .teams-header {
        flex-direction: column;
        text-align: center;
    }

    .teams-stats {
        justify-content: center;
    }

    .teams-grid {
        grid-template-columns: 1fr;
        padding: 20px;
        gap: 20px;
    }

    .team-card-header {
        flex-direction: column;
        text-align: center;
    }

    .team-actions {
        flex-direction: column;
    }

    .btn {
        justify-content: center;
    }
}

/* Loading Animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.team-card.loading {
    animation: pulse 1.5s ease-in-out infinite;
    pointer-events: none;
}

/* Ripple Effect */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Enhanced Hover Effects */
.team-logo img,
.team-logo-placeholder {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth Scrolling Enhancement */
html {
    scroll-behavior: smooth;
}

/* Enhanced Card Animations */
.team-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.team-card:nth-child(1) { animation-delay: 0.1s; }
.team-card:nth-child(2) { animation-delay: 0.2s; }
.team-card:nth-child(3) { animation-delay: 0.3s; }
.team-card:nth-child(4) { animation-delay: 0.4s; }
.team-card:nth-child(5) { animation-delay: 0.5s; }
.team-card:nth-child(6) { animation-delay: 0.6s; }

/* Modern Scrollbar */
.teams-section::-webkit-scrollbar {
    width: 8px;
}

.teams-section::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.teams-section::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

.teams-section::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Enhanced Focus States */
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.team-card:focus-within {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

/* Dark Mode Support (if needed) */
@media (prefers-color-scheme: dark) {
    .teams-section {
        background: #1e1e1e;
        color: #ffffff;
    }

    .team-card {
        background: #2d2d2d;
        border-color: #404040;
    }

    .team-card-header {
        background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%);
        border-color: #404040;
    }

    .team-name {
        color: #ffffff;
    }

    .team-description,
    .team-stats .stat {
        color: #b0b0b0;
    }

    .team-card-footer {
        background: #2d2d2d;
        border-color: #404040;
    }
}

/* Print Styles */
@media print {
    .quick-team-creation,
    .team-actions,
    .btn {
        display: none !important;
    }

    .teams-grid {
        display: block !important;
    }

    .team-card {
        break-inside: avoid;
        margin-bottom: 20px;
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}

/* Accessibility Improvements */
.btn:focus-visible {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.team-card:focus-visible {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .team-card {
        border: 2px solid #000;
    }

    .btn {
        border: 2px solid currentColor;
    }

    .teams-header {
        background: #000;
        color: #fff;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .team-card,
    .btn,
    .member-avatar,
    .team-logo img,
    .team-logo-placeholder {
        transition: none !important;
        animation: none !important;
    }

    .scroll-to-teams::after {
        animation: none !important;
    }
}
</style>