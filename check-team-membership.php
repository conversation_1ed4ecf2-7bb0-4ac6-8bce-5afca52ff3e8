<?php
/**
 * Simple script to check team membership
 * Add this to your WordPress root and access it via browser
 */

// Load WordPress
require_once('wp-load.php');

// Only run for logged-in users
if (!is_user_logged_in()) {
    die('يجب تسجيل الدخول أولاً');
}

$current_user_id = get_current_user_id();
$current_user = wp_get_current_user();

echo "<h1>فحص عضوية الفرق</h1>";
echo "<p><strong>المستخدم الحالي:</strong> " . $current_user->display_name . " (ID: $current_user_id)</p>";

global $wpdb;

// Check team_members table structure
echo "<h2>بنية جدول team_members</h2>";
$columns = $wpdb->get_col("DESCRIBE {$wpdb->prefix}team_members");
echo "<p><strong>الأعمدة:</strong> " . implode(', ', $columns) . "</p>";

// Show all teams
echo "<h2>جميع الفرق</h2>";
$teams = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}teams ORDER BY id");
if ($teams) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>المنشئ</th></tr>";
    foreach ($teams as $team) {
        echo "<tr>";
        echo "<td>" . $team->id . "</td>";
        echo "<td>" . esc_html($team->name) . "</td>";
        echo "<td>" . $team->created_by . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد فرق</p>";
}

// Show current user's team memberships
echo "<h2>عضوية المستخدم الحالي</h2>";
$user_memberships = $wpdb->get_results($wpdb->prepare(
    "SELECT tm.*, t.name as team_name 
     FROM {$wpdb->prefix}team_members tm
     LEFT JOIN {$wpdb->prefix}teams t ON tm.team_id = t.id
     WHERE tm.user_id = %d",
    $current_user_id
));

if ($user_memberships) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>فريق ID</th><th>اسم الفريق</th><th>الدور</th><th>الحالة</th><th>تاريخ الانضمام</th></tr>";
    foreach ($user_memberships as $membership) {
        $status = '';
        if (isset($membership->status)) {
            $status = $membership->status;
        } elseif (isset($membership->is_active)) {
            $status = $membership->is_active ? 'active' : 'inactive';
        } else {
            $status = 'unknown';
        }
        
        echo "<tr>";
        echo "<td>" . $membership->team_id . "</td>";
        echo "<td>" . esc_html($membership->team_name) . "</td>";
        echo "<td>" . esc_html($membership->role) . "</td>";
        echo "<td>" . esc_html($status) . "</td>";
        echo "<td>" . $membership->joined_at . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>المستخدم ليس عضواً في أي فريق</p>";
}

// Test team membership function if we have teams
if ($teams && $user_memberships) {
    echo "<h2>اختبار دالة is_user_team_member</h2>";
    
    // Get the admin class instance
    $admin_class = new Team_System_Admin('team-system', '1.0.0');
    
    foreach ($user_memberships as $membership) {
        // Use reflection to access private method
        $reflection = new ReflectionClass($admin_class);
        $method = $reflection->getMethod('is_user_team_member');
        $method->setAccessible(true);
        
        $is_member = $method->invoke($admin_class, $current_user_id, $membership->team_id);
        
        echo "<p><strong>فريق " . esc_html($membership->team_name) . " (ID: " . $membership->team_id . "):</strong> ";
        echo $is_member ? '<span style="color: green;">عضو ✓</span>' : '<span style="color: red;">ليس عضو ✗</span>';
        echo "</p>";
    }
}

// Show some team chapters if they exist
echo "<h2>فصول الفرق</h2>";
$team_chapters = $wpdb->get_results(
    "SELECT p.ID, p.post_title, p.post_author, pm1.meta_value as publish_as_team, pm2.meta_value as team_id, t.name as team_name
     FROM {$wpdb->posts} p
     LEFT JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_publish_as_team'
     LEFT JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_team_id'
     LEFT JOIN {$wpdb->prefix}teams t ON t.id = pm2.meta_value
     WHERE p.post_type = 'chapter' AND pm1.meta_value = '1'
     ORDER BY p.ID DESC
     LIMIT 10"
);

if ($team_chapters) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>فصل ID</th><th>العنوان</th><th>المؤلف</th><th>فريق ID</th><th>اسم الفريق</th><th>يمكن التعديل؟</th></tr>";
    foreach ($team_chapters as $chapter) {
        $can_edit = current_user_can('edit_post', $chapter->ID);
        $can_edit_text = $can_edit ? '<span style="color: green;">نعم ✓</span>' : '<span style="color: red;">لا ✗</span>';
        
        echo "<tr>";
        echo "<td>" . $chapter->ID . "</td>";
        echo "<td>" . esc_html($chapter->post_title) . "</td>";
        echo "<td>" . $chapter->post_author . "</td>";
        echo "<td>" . $chapter->team_id . "</td>";
        echo "<td>" . esc_html($chapter->team_name) . "</td>";
        echo "<td>" . $can_edit_text . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>لا توجد فصول منشورة كفريق</p>";
}

echo "<hr>";
echo "<p><em>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</em></p>";
echo "<p><strong>ملاحظة:</strong> تحقق من سجلات الأخطاء في WordPress لمزيد من التفاصيل.</p>";
?>
