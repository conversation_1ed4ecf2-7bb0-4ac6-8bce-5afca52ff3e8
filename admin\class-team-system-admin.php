<?php
class Team_System_Admin {
    private $plugin_name;
    private $version;

    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
        
        // Generate slugs for existing teams if needed
        add_action('admin_init', array($this, 'maybe_generate_slugs_for_existing_teams'));

        // Update database structure if needed
        add_action('admin_init', array($this, 'update_database_structure'));

        // Create database tables on plugin activation
        register_activation_hook(plugin_dir_path(dirname(__FILE__)) . 'team-system.php', array($this, 'create_tables'));
        
        add_action('admin_menu', array($this, 'add_plugin_admin_menu'));
        add_action('admin_init', array($this, 'register_team_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_styles'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_bar_menu', array($this, 'add_admin_bar_notification'), 100);
        add_action('init', array($this, 'add_translator_capabilities'));
        
        // Add AJAX actions
        add_action('wp_ajax_team_system_add_member', array($this, 'ajax_add_member'));
        add_action('wp_ajax_team_system_remove_member', array($this, 'ajax_remove_member'));
        add_action('wp_ajax_check_team_slug_availability', array($this, 'ajax_check_slug_availability'));
        add_action('wp_ajax_team_system_accept_invitation', array($this, 'ajax_accept_invitation'));
        add_action('wp_ajax_team_system_decline_invitation', array($this, 'ajax_decline_invitation'));
        add_action('wp_ajax_team_system_cancel_invitation', array($this, 'ajax_cancel_invitation'));
        add_action('wp_ajax_team_system_delete_team', array($this, 'ajax_delete_team'));
        add_action('wp_ajax_get_team_members', array($this, 'ajax_get_team_members'));

        // Add team publishing features for chapters
        add_action('add_meta_boxes', array($this, 'add_team_meta_boxes'));
        add_action('save_post', array($this, 'save_team_chapter_meta'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_team_admin_scripts'));

        // COMPREHENSIVE TEAM CHAPTER EDITING SYSTEM
        // Layer 1: Early capability mapping (highest priority)
        add_filter('map_meta_cap', array($this, 'map_team_chapter_capabilities'), 1, 4);

        // Layer 2: User capability enhancement (very high priority)
        add_filter('user_has_cap', array($this, 'enhance_team_member_capabilities'), 1, 4);

        // Layer 3: Early initialization of team capabilities
        add_action('init', array($this, 'initialize_team_capabilities'), 1);
        add_action('wp_loaded', array($this, 'setup_team_member_capabilities'), 1);

        // Layer 4: Admin-specific capability setup
        add_action('admin_init', array($this, 'setup_admin_team_capabilities'), 1);
        add_action('current_screen', array($this, 'setup_screen_team_capabilities'), 1);

        // Layer 5: Post list integration
        add_filter('post_row_actions', array($this, 'ensure_team_edit_links'), 10, 2);
        add_filter('page_row_actions', array($this, 'ensure_team_edit_links'), 10, 2);

        // Layer 6: Direct edit permission override
        add_filter('user_can_edit_post', array($this, 'allow_team_chapter_editing'), 10, 3);

        // Add admin notices for team chapter editing
        add_action('admin_notices', array($this, 'show_team_chapter_edit_notices'));

        // Add team column to chapters list
        add_filter('manage_chapter_posts_columns', array($this, 'add_team_column_to_chapters'));
        add_action('manage_chapter_posts_custom_column', array($this, 'display_team_column_content'), 10, 2);

        // Integrate team chapters into "mine" filter
        add_filter('parse_query', array($this, 'include_team_chapters_in_mine'));
        
        // Handle form submission
        add_action('admin_init', array($this, 'handle_add_team'));
        
        // Handle update tables action
        if (isset($_GET['update_team_tables']) && current_user_can('manage_options')) {
            $this->update_tables();
            add_action('admin_notices', function() {
                echo '<div class="notice notice-success is-dismissible"><p>' . 
                     __('تم تحديث جداول قاعدة البيانات بنجاح', 'team-system') . 
                     '</p></div>';
            });
        }
        
        // Create or update tables if needed
        $this->update_tables();
        
        // Add update tables link to admin bar
        add_action('admin_bar_menu', array($this, 'add_update_tables_link'), 100);
    }

    public function enqueue_styles() {
        wp_enqueue_style(
            $this->plugin_name,
            plugin_dir_url(__FILE__) . 'css/team-system-admin.css',
            array(),
            $this->version,
            'all'
        );
    }

    public function enqueue_scripts() {
        wp_enqueue_script(
            $this->plugin_name,
            plugin_dir_url(__FILE__) . 'js/team-system-admin.js',
            array('jquery'),
            $this->version,
            false
        );
        
        wp_localize_script($this->plugin_name, 'teamSystemAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('team_system_nonce'),
            'confirmUpdate' => __('هل أنت متأكد من رغبتك في تحديث جداول قاعدة البيانات؟', 'team-system')
        ));
    }

    /**
     * Add capabilities to translator role for team system access
     */
    public function add_translator_capabilities() {
        // Only run this once to avoid repeated capability additions
        if (get_option('team_system_capabilities_added')) {
            return;
        }

        // Get the translator role
        $translator_role = get_role('translator');

        if ($translator_role) {
            // Add team system capabilities to translator role
            $translator_role->add_cap('access_team_system');
            $translator_role->add_cap('create_teams');
            $translator_role->add_cap('join_teams');
            // Add basic WordPress capability if not present
            $translator_role->add_cap('edit_posts');
        }

        // Also add to other roles that should have access
        $roles_with_access = array('administrator', 'editor', 'author');
        foreach ($roles_with_access as $role_name) {
            $role = get_role($role_name);
            if ($role) {
                $role->add_cap('access_team_system');
                $role->add_cap('create_teams');
                $role->add_cap('join_teams');
            }
        }

        // Mark capabilities as added
        update_option('team_system_capabilities_added', true);
    }

    /**
     * Get required capability for team system access
     * Allows translators and above to access team system
     */
    private function get_required_capability() {
        // Check capabilities in order of preference
        if (current_user_can('manage_options')) {
            return 'manage_options';  // Administrators
        } elseif (current_user_can('access_team_system')) {
            return 'access_team_system';  // Users with team system access
        } elseif (current_user_can('edit_others_posts')) {
            return 'edit_others_posts';  // Editors
        } elseif (current_user_can('publish_posts')) {
            return 'publish_posts';  // Authors
        } else {
            return 'edit_posts';  // Contributors and above
        }
    }

    public function add_plugin_admin_menu() {
        // Get the required capability - allow translators and above
        $required_cap = $this->get_required_capability();

        add_menu_page(
            'إدارة الفرق',
            'الفرق',
            $required_cap,  // Use dynamic capability
            'team-system',
            array($this, 'display_plugin_admin_page'),
            'dashicons-groups',
            26
        );
        
        add_submenu_page(
            'team-system',
            'إضافة فريق جديد',
            'إضافة فريق جديد',
            $required_cap,  // Use same capability as main menu
            'team-system-add',
            array($this, 'display_add_team_page')
        );
    }

    public function register_team_settings() {
        register_setting('team_system_team_settings', 'team_system_options');
    }

    /**
     * Add update tables link to admin bar
     */
    public function add_update_tables_link($wp_admin_bar) {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        $args = array(
            'id'    => 'update-team-tables',
            'title' => 'تحديث جداول الفرق',
            'href'  => wp_nonce_url(admin_url('admin.php?page=team-system&update_team_tables=1'), 'update_team_tables'),
            'meta'  => array(
                'class' => 'update-team-tables',
                'title' => 'تحديث جداول قاعدة بيانات الفرق',
                'onclick' => 'return confirm("هل أنت متأكد من رغبتك في تحديث جداول قاعدة البيانات؟");'
            )
        );
        $wp_admin_bar->add_node($args);
    }
    
    public function display_plugin_admin_page() {
        // Check if user has permission using the same logic as menu
        $required_cap = $this->get_required_capability();
        if (!current_user_can($required_cap)) {
            wp_die(__('عذرًا، غير مسموح لك الوصول إلى هذه الصفحة.'));
        }
        
        if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['team_id'])) {
            $this->display_edit_team_page();
        } else {
            include_once 'partials/team-system-admin-display.php';
        }
    }
    /**
     * Create necessary database tables
     */
    /**
     * Update database tables if needed
     */
    public function update_tables() {
        global $wpdb;
        
        // Check if we need to update the tables
        $table_teams = $wpdb->prefix . 'teams';
        $table_team_members = $wpdb->prefix . 'team_members';
        
        // Check if tables exist
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_teams'") == $table_teams &&
            $wpdb->get_var("SHOW TABLES LIKE '$table_team_members'") == $table_team_members) {
            // Tables exist, check if we need to update them
            $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_teams LIKE 'cover_url'");
            if (!empty($columns) && strpos(strtolower($columns[0]->Type), 'varchar(255)') !== false) {
                // Update existing tables
                $this->update_existing_tables();
            }
        } else {
            // Tables don't exist, create them
            $this->create_tables();
        }
    }
    
    /**
     * Update existing tables structure
     */
    private function update_existing_tables() {
        global $wpdb;

        $table_teams = $wpdb->prefix . 'teams';

        // Modify columns to increase their length
        $wpdb->query("ALTER TABLE $table_teams MODIFY COLUMN logo_url VARCHAR(1000) DEFAULT NULL");
        $wpdb->query("ALTER TABLE $table_teams MODIFY COLUMN cover_url VARCHAR(1000) DEFAULT NULL");

        // Add slug column if it doesn't exist
        $slug_column = $wpdb->get_results("SHOW COLUMNS FROM $table_teams LIKE 'slug'");
        if (empty($slug_column)) {
            $wpdb->query("ALTER TABLE $table_teams ADD COLUMN slug VARCHAR(255) AFTER name");

            // Generate slugs for existing teams
            $this->maybe_generate_slugs_for_existing_teams();
        }

        // Add social media columns if they don't exist
        $social_columns = array(
            'website_url' => 'VARCHAR(255) DEFAULT NULL',
            'facebook_url' => 'VARCHAR(255) DEFAULT NULL',
            'twitter_url' => 'VARCHAR(255) DEFAULT NULL',
            'discord_url' => 'VARCHAR(255) DEFAULT NULL',
            'privacy' => 'VARCHAR(20) DEFAULT "public"'
        );

        foreach ($social_columns as $column => $definition) {
            $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_teams LIKE '$column'");
            if (empty($column_exists)) {
                $wpdb->query("ALTER TABLE $table_teams ADD COLUMN $column $definition");
            }
        }
    }

    /**
     * Update database structure
     */
    public function update_database_structure() {
        // Run only once per version
        $db_version = get_option('team_system_db_version', '1.0.0');
        if (version_compare($db_version, '1.1.0', '<')) {
            $this->update_existing_tables();
            update_option('team_system_db_version', '1.1.0');
        }
    }

    /**
     * Create necessary database tables
     * @param bool $force_update Whether to force table update
     */
    public function create_tables($force_update = false) {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = [];
        
        // Teams table
        $table_teams = $wpdb->prefix . 'teams';
        if ($force_update || $wpdb->get_var("SHOW TABLES LIKE '$table_teams'") != $table_teams) {
            $sql[] = "CREATE TABLE $table_teams (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                name varchar(255) NOT NULL,
                slug varchar(255) NOT NULL,
                description longtext,
                logo_url varchar(1000) DEFAULT NULL,
                cover_url varchar(1000) DEFAULT NULL,
                website_url varchar(255) DEFAULT NULL,
                facebook_url varchar(255) DEFAULT NULL,
                twitter_url varchar(255) DEFAULT NULL,
                discord_url varchar(255) DEFAULT NULL,
                privacy varchar(20) DEFAULT 'public',
                created_by bigint(20) NOT NULL,
                created_at datetime NOT NULL,
                updated_at datetime NOT NULL,
                status varchar(20) NOT NULL DEFAULT 'active',
                PRIMARY KEY  (id),
                KEY created_by (created_by),
                KEY status (status)
            ) $charset_collate;";
        }
        
        // Team members table
        $table_team_members = $wpdb->prefix . 'team_members';
        if ($force_update || $wpdb->get_var("SHOW TABLES LIKE '$table_team_members'") != $table_team_members) {
            $sql[] = "CREATE TABLE $table_team_members (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                team_id bigint(20) NOT NULL,
                user_id bigint(20) NOT NULL,
                role varchar(50) NOT NULL DEFAULT 'member',
                joined_at datetime NOT NULL,
                status varchar(20) DEFAULT 'active',
                PRIMARY KEY  (id),
                UNIQUE KEY team_user (team_id, user_id),
                KEY user_id (user_id)
            ) $charset_collate;";
        }

        // Team invitations table
        $table_team_invitations = $wpdb->prefix . 'team_invitations';
        if ($force_update || $wpdb->get_var("SHOW TABLES LIKE '$table_team_invitations'") != $table_team_invitations) {
            $sql[] = "CREATE TABLE $table_team_invitations (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                team_id bigint(20) NOT NULL,
                email varchar(100) NOT NULL,
                user_id bigint(20) DEFAULT NULL,
                role varchar(50) NOT NULL,
                token varchar(100) NOT NULL,
                invited_by bigint(20) NOT NULL,
                created_at datetime NOT NULL,
                expires_at datetime NOT NULL,
                status varchar(20) DEFAULT 'pending',
                PRIMARY KEY  (id),
                UNIQUE KEY token (token),
                KEY team_id (team_id),
                KEY user_id (user_id),
                KEY status (status)
            ) $charset_collate;";
        }
        
        if (!empty($sql)) {
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }
    
    /**
     * Handle adding a new team
     */
    public function handle_add_team() {
        // Check if form was submitted
        if (!isset($_POST['team_system_nonce']) || !wp_verify_nonce($_POST['team_system_nonce'], 'team_system_add_team')) {
            return;
        }

        // Check user capabilities using the same logic as menu
        $required_cap = $this->get_required_capability();
        if (!current_user_can($required_cap)) {
            wp_die(__('عذرًا، غير مسموح لك بتنفيذ هذا الإجراء.'));
        }

        // Validate required fields
        if (empty($_POST['team_name'])) {
            add_settings_error('team_system_messages', 'team_system_message', __('يرجى إدخال اسم الفريق', 'team-system'), 'error');
            return;
        }

        global $wpdb;
        
        // Handle file uploads
        $logo_url = $this->handle_file_upload('team_logo');
        $cover_url = $this->handle_file_upload('team_cover');

        // Handle team slug
        $team_slug = '';
        if (!empty($_POST['team_slug'])) {
            // User provided custom slug
            $team_slug = $this->sanitize_team_slug($_POST['team_slug']);

            // Validate slug format
            if (!$this->validate_team_slug($team_slug)) {
                add_settings_error('team_system_messages', 'team_system_message',
                    __('رابط الفريق يجب أن يحتوي على أحرف إنجليزية صغيرة وأرقام وشرطات فقط، وأن يكون 3 أحرف على الأقل', 'team-system'), 'error');
                return;
            }
        } else {
            // Generate slug from team name
            $team_slug = $this->generate_slug_from_name($_POST['team_name']);
        }

        // Ensure slug is unique
        $team_slug = $this->get_unique_team_slug($team_slug);
        
        // Prepare team data
        $team_data = array(
            'name' => sanitize_text_field($_POST['team_name']),
            'slug' => $team_slug,
            'description' => wp_kses_post($_POST['team_description']),
            'created_by' => get_current_user_id(),
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql'),
            'status' => 'active'
        );
        
        // Add logo/cover URLs if files were uploaded
        if ($logo_url) $team_data['logo_url'] = $logo_url;
        if ($cover_url) $team_data['cover_url'] = $cover_url;
        
        // Insert team into database
        $result = $wpdb->insert(
            $wpdb->prefix . 'teams',
            $team_data,
            array(
                '%s', // name
                '%s', // slug
                '%s', // description
                '%d', // created_by
                '%s', // created_at
                '%s', // updated_at
                '%s', // status
                '%s', // logo_url (optional)
                '%s'  // cover_url (optional)
            )
        );
        
        if ($result === false) {
            // Get detailed error message
            $error_message = $wpdb->last_error ?: __('خطأ غير معروف', 'team-system');
            add_settings_error(
                'team_system_messages', 
                'team_system_message', 
                sprintf(__('حدث خطأ أثناء حفظ الفريق: %s', 'team-system'), $error_message),
                'error'
            );
            
            // Log the error for debugging
            error_log('Team System Error: ' . $error_message);
            error_log('Query: ' . $wpdb->last_query);
            error_log('Data: ' . print_r($team_data, true));
        } else {
            // Add current user as team leader
            $wpdb->insert(
                $wpdb->prefix . 'team_members',
                array(
                    'team_id' => $wpdb->insert_id,
                    'user_id' => get_current_user_id(),
                    'role' => 'leader',
                    'joined_at' => current_time('mysql')
                ),
                array('%d', '%d', '%s', '%s')
            );
            
            add_settings_error('team_system_messages', 'team_system_message', __('تم إضافة الفريق بنجاح', 'team-system'), 'success');
            
            // Redirect to teams list
            wp_redirect(admin_url('admin.php?page=team-system&message=added'));
            exit;
        }
    }

    public function display_add_team_page() {
        // Check if user has permission using the same logic as menu
        $required_cap = $this->get_required_capability();
        if (!current_user_can($required_cap)) {
            wp_die(__('عذرًا، غير مسموح لك الوصول إلى هذه الصفحة.'));
        }
        
        // Show admin notices
        settings_errors('team_system_messages');
        
        include_once 'partials/team-system-add-team.php';
    }

    public function display_edit_team_page() {
        include_once 'partials/team-system-edit-team.php';
    }

    private function get_team_by_id($team_id) {
        global $wpdb;
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}teams WHERE id = %d",
            $team_id
        ));
    }

    private function get_team_members($team_id) {
        global $wpdb;
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}team_members WHERE team_id = %d",
            $team_id
        ));
    }

    private function get_available_roles() {
        return array(
            'leader' => 'قائد الفريق',
            'translator' => 'مترجم',
            'editor' => 'مدقق لغوي',
            'reviewer' => 'مراجع جودة',
            'designer' => 'مصمم رسوم'
        );
    }

    public function get_role_name($role) {
        $roles = $this->get_available_roles();
        return isset($roles[$role]) ? $roles[$role] : $role;
    }

    private function update_team($team_id, $data) {
        global $wpdb;
        
        // Handle file uploads
        $logo_url = $this->handle_file_upload('team_logo');
        $cover_url = $this->handle_file_upload('team_cover');
        
        // Handle team slug update
        $new_slug = '';
        if (!empty($data['team_slug'])) {
            $new_slug = $this->sanitize_team_slug($data['team_slug']);

            // Validate slug format
            if (!$this->validate_team_slug($new_slug)) {
                error_log('Invalid team slug format: ' . $new_slug);
                return false;
            }

            // Make sure slug is unique (excluding current team)
            $new_slug = $this->get_unique_team_slug($new_slug, $team_id);
        }

        // Prepare team data
        $team_data = array(
            'name' => sanitize_text_field($data['team_name']),
            'description' => wp_kses_post($data['team_description']),
            'website_url' => esc_url_raw($data['team_website']),
            'facebook_url' => esc_url_raw($data['team_facebook']),
            'twitter_url' => esc_url_raw($data['team_twitter']),
            'discord_url' => esc_url_raw($data['team_discord']),
            'privacy' => sanitize_text_field($data['team_privacy']),
            'updated_at' => current_time('mysql')
        );

        // Add slug to update data if provided
        if (!empty($new_slug)) {
            $team_data['slug'] = $new_slug;
        }
        
        // Add logo/cover URLs if files were uploaded
        if ($logo_url) $team_data['logo_url'] = $logo_url;
        if ($cover_url) $team_data['cover_url'] = $cover_url;
        
        // Update team in database
        $result = $wpdb->update(
            $wpdb->prefix . 'teams',
            $team_data,
            array('id' => $team_id),
            null, // Let WordPress determine the format automatically
            array('%d')
        );

        // Check for errors
        if ($result === false) {
            error_log('Team update failed: ' . $wpdb->last_error);
            error_log('Team data: ' . print_r($team_data, true));
        }
        
        // Update member roles
        if (!empty($data['member_roles']) && is_array($data['member_roles'])) {
            foreach ($data['member_roles'] as $user_id => $role) {
                $wpdb->update(
                    $wpdb->prefix . 'team_members',
                    array('role' => sanitize_text_field($role)),
                    array(
                        'team_id' => $team_id,
                        'user_id' => intval($user_id)
                    )
                );
            }
        }
    }

    private function handle_file_upload($field_name) {
        if (!empty($_FILES[$field_name]['name'])) {
            $upload = wp_handle_upload($_FILES[$field_name], array('test_form' => false));
            if (!isset($upload['error'])) {
                return $upload['url'];
            }
        }
        return '';
    }

    public function ajax_add_member() {
        check_ajax_referer('team_system_nonce', 'nonce');

        $team_id = isset($_POST['team_id']) ? intval($_POST['team_id']) : 0;
        $username = isset($_POST['username']) ? sanitize_text_field($_POST['username']) : '';
        $role = isset($_POST['role']) ? sanitize_text_field($_POST['role']) : 'member';

        // Validate input
        if (!$team_id || !$username) {
            wp_send_json_error(array('message' => 'بيانات غير صالحة'));
        }

        // Check if user exists by username or email
        $user = false;
        if (is_email($username)) {
            $user = get_user_by('email', $username);
        } else {
            $user = get_user_by('login', $username);
        }

        if (!$user) {
            wp_send_json_error(array('message' => 'لم يتم العثور على مستخدم بهذا الاسم أو البريد الإلكتروني'));
        }

        global $wpdb;

        // Check if user is already a member
        $existing_member = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}team_members
             WHERE team_id = %d AND user_id = %d",
            $team_id,
            $user->ID
        ));

        if ($existing_member) {
            wp_send_json_error(array('message' => 'هذا العضو موجود بالفعل في الفريق'));
        }

        // Check if there's already a pending invitation
        $existing_invitation = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}team_invitations
             WHERE team_id = %d AND user_id = %d AND status = 'pending'",
            $team_id,
            $user->ID
        ));

        if ($existing_invitation) {
            wp_send_json_error(array('message' => 'تم إرسال دعوة لهذا المستخدم مسبقاً وهي في انتظار الرد'));
        }

        // Create invitation
        $token = md5(uniqid() . time() . $user->ID . $team_id);
        $expires_at = date('Y-m-d H:i:s', strtotime('+7 days')); // Expires in 7 days
        $current_time = current_time('mysql');

        $result = $wpdb->insert(
            $wpdb->prefix . 'team_invitations',
            array(
                'team_id' => $team_id,
                'email' => $user->user_email,
                'user_id' => $user->ID,
                'role' => $role,
                'token' => $token,
                'invited_by' => get_current_user_id(),
                'created_at' => $current_time,
                'expires_at' => $expires_at,
                'status' => 'pending'
            ),
            array('%d', '%s', '%d', '%s', '%s', '%d', '%s', '%s', '%s')
        );

        if ($result) {
            // Log success for debugging
            error_log('Team invitation sent successfully: Team ID ' . $team_id . ', User ID ' . $user->ID);
            wp_send_json_success(array('message' => 'تم إرسال دعوة الانضمام للفريق بنجاح'));
        } else {
            // Log error for debugging
            error_log('Team invitation failed: ' . $wpdb->last_error);
            error_log('Query: ' . $wpdb->last_query);
            wp_send_json_error(array('message' => 'حدث خطأ أثناء إرسال الدعوة: ' . $wpdb->last_error));
        }
    }

    public function ajax_remove_member() {
        check_ajax_referer('team_system_nonce', 'nonce');
        
        $team_id = isset($_POST['team_id']) ? intval($_POST['team_id']) : 0;
        $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
        
        if (!$team_id || !$user_id) {
            wp_send_json_error(array('message' => 'بيانات غير صالحة'));
        }
        
        // Don't allow removing the team leader
        global $wpdb;
        $team = $wpdb->get_row($wpdb->prepare(
            "SELECT created_by FROM {$wpdb->prefix}teams WHERE id = %d",
            $team_id
        ));
        
        if ($team && $team->created_by == $user_id) {
            wp_send_json_error(array('message' => 'لا يمكن إزالة قائد الفريق'));
        }
        
        // Remove member
        $result = $wpdb->delete(
            $wpdb->prefix . 'team_members',
            array(
                'team_id' => $team_id,
                'user_id' => $user_id
            ),
            array('%d', '%d')
        );
        
        if ($result) {
            wp_send_json_success();
        } else {
            wp_send_json_error(array('message' => 'حدث خطأ أثناء إزالة العضو'));
        }
    }

    public function ajax_delete_team() {
        check_ajax_referer('team_system_nonce', 'nonce');
        
        $team_id = isset($_POST['team_id']) ? intval($_POST['team_id']) : 0;
        if (!$team_id) {
            wp_send_json_error(array('message' => 'بيانات غير صالحة'));
        }
        
        // Check permissions
        if (!current_user_can('delete_teams')) {
            wp_send_json_error(array('message' => 'ليس لديك صلاحية لحذف الفرق'));
        }
        
        global $wpdb;
        
        // Delete team members
        $wpdb->delete(
            $wpdb->prefix . 'team_members',
            array('team_id' => $team_id),
            array('%d')
        );
        
        // Delete team
        $result = $wpdb->delete(
            $wpdb->prefix . 'teams',
            array('id' => $team_id),
            array('%d')
        );
        
        if ($result) {
            wp_send_json_success();
        } else {
            wp_send_json_error(array('message' => 'حدث خطأ أثناء حذف الفريق'));
        }
    }
    
    /**
     * Generate slugs for existing teams that don't have one
     */
    public function maybe_generate_slugs_for_existing_teams() {
        // Only run once and only for admins
        if (!current_user_can('manage_options') || get_option('team_system_slugs_generated')) {
            return;
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'teams';
        
        // Get teams without a slug
        $teams = $wpdb->get_results("SELECT id, name FROM {$table_name} WHERE slug = '' OR slug IS NULL");
        
        if (!empty($teams)) {
            foreach ($teams as $team) {
                $slug = sanitize_title($team->name);
                $unique_slug = $this->get_unique_team_slug($slug, $team->id);
                
                $wpdb->update(
                    $table_name,
                    array('slug' => $unique_slug),
                    array('id' => $team->id),
                    array('%s'),
                    array('%d')
                );
            }
        }
        
        // Mark as done
        update_option('team_system_slugs_generated', '1');
    }
    
    /**
     * Get a unique team slug
     */
    private function get_unique_team_slug($slug, $exclude_id = 0) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'teams';

        // Check if slug exists
        $query = $wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} WHERE slug = %s AND id != %d",
            $slug,
            $exclude_id
        );

        if ($wpdb->get_var($query) == 0) {
            return $slug;
        }

        // If slug exists, append a number and try again
        $count = 2;
        while (true) {
            $new_slug = $slug . '-' . $count;
            $query = $wpdb->prepare(
                "SELECT COUNT(*) FROM {$table_name} WHERE slug = %s AND id != %d",
                $new_slug,
                $exclude_id
            );

            if ($wpdb->get_var($query) == 0) {
                return $new_slug;
            }

            $count++;
        }
    }

    /**
     * Sanitize team slug
     */
    private function sanitize_team_slug($slug) {
        // Convert to lowercase and remove unwanted characters
        $slug = strtolower(trim($slug));
        $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);
        $slug = preg_replace('/-+/', '-', $slug); // Replace multiple hyphens with single
        $slug = trim($slug, '-'); // Remove leading/trailing hyphens
        return $slug;
    }

    /**
     * Validate team slug format
     */
    private function validate_team_slug($slug) {
        return !empty($slug) &&
               strlen($slug) >= 3 &&
               preg_match('/^[a-z0-9\-]+$/', $slug) &&
               !preg_match('/^-|-$/', $slug); // No leading/trailing hyphens
    }

    /**
     * Generate slug from team name
     */
    private function generate_slug_from_name($name) {
        // Remove Arabic characters and convert to slug format
        $slug = strtolower(trim($name));
        $slug = preg_replace('/[أ-ي]/', '', $slug); // Remove Arabic characters
        $slug = preg_replace('/[^a-z0-9\s\-]/', '', $slug); // Keep only allowed characters
        $slug = preg_replace('/\s+/', '-', $slug); // Replace spaces with hyphens
        $slug = preg_replace('/-+/', '-', $slug); // Replace multiple hyphens with single
        $slug = trim($slug, '-'); // Remove leading/trailing hyphens

        // If slug is empty or too short, generate a default one
        if (empty($slug) || strlen($slug) < 3) {
            $slug = 'team-' . uniqid();
        }

        return $slug;
    }

    /**
     * AJAX handler to accept team invitation
     */
    public function ajax_accept_invitation() {
        check_ajax_referer('team_system_nonce', 'nonce');

        $invitation_id = isset($_POST['invitation_id']) ? intval($_POST['invitation_id']) : 0;

        if (!$invitation_id) {
            wp_send_json_error(array('message' => 'معرف الدعوة غير صالح'));
        }

        global $wpdb;

        // Get invitation details
        $invitation = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}team_invitations WHERE id = %d AND status = 'pending'",
            $invitation_id
        ));

        if (!$invitation) {
            wp_send_json_error(array('message' => 'الدعوة غير موجودة أو منتهية الصلاحية'));
        }

        // Check if invitation is expired
        if (strtotime($invitation->expires_at) < time()) {
            wp_send_json_error(array('message' => 'انتهت صلاحية هذه الدعوة'));
        }

        // Check if user is the invited user
        if ($invitation->user_id != get_current_user_id()) {
            wp_send_json_error(array('message' => 'غير مسموح لك بقبول هذه الدعوة'));
        }

        // Add user to team
        $result = $wpdb->insert(
            $wpdb->prefix . 'team_members',
            array(
                'team_id' => $invitation->team_id,
                'user_id' => $invitation->user_id,
                'role' => $invitation->role,
                'joined_at' => current_time('mysql'),
                'status' => 'active'
            ),
            array('%d', '%d', '%s', '%s', '%s')
        );

        if ($result) {
            // Update invitation status
            $wpdb->update(
                $wpdb->prefix . 'team_invitations',
                array('status' => 'accepted'),
                array('id' => $invitation_id),
                array('%s'),
                array('%d')
            );

            wp_send_json_success(array('message' => 'تم قبول الدعوة بنجاح! مرحباً بك في الفريق'));
        } else {
            wp_send_json_error(array('message' => 'حدث خطأ أثناء قبول الدعوة'));
        }
    }

    /**
     * AJAX handler to decline team invitation
     */
    public function ajax_decline_invitation() {
        check_ajax_referer('team_system_nonce', 'nonce');

        $invitation_id = isset($_POST['invitation_id']) ? intval($_POST['invitation_id']) : 0;

        if (!$invitation_id) {
            wp_send_json_error(array('message' => 'معرف الدعوة غير صالح'));
        }

        global $wpdb;

        // Get invitation details
        $invitation = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}team_invitations WHERE id = %d AND status = 'pending'",
            $invitation_id
        ));

        if (!$invitation) {
            wp_send_json_error(array('message' => 'الدعوة غير موجودة'));
        }

        // Check if user is the invited user
        if ($invitation->user_id != get_current_user_id()) {
            wp_send_json_error(array('message' => 'غير مسموح لك برفض هذه الدعوة'));
        }

        // Update invitation status
        $result = $wpdb->update(
            $wpdb->prefix . 'team_invitations',
            array('status' => 'declined'),
            array('id' => $invitation_id),
            array('%s'),
            array('%d')
        );

        if ($result) {
            wp_send_json_success(array('message' => 'تم رفض الدعوة'));
        } else {
            wp_send_json_error(array('message' => 'حدث خطأ أثناء رفض الدعوة'));
        }
    }

    /**
     * AJAX handler to cancel team invitation (by team leader)
     */
    public function ajax_cancel_invitation() {
        check_ajax_referer('team_system_nonce', 'nonce');

        $invitation_id = isset($_POST['invitation_id']) ? intval($_POST['invitation_id']) : 0;

        if (!$invitation_id) {
            wp_send_json_error(array('message' => 'معرف الدعوة غير صالح'));
        }

        global $wpdb;

        // Get invitation details
        $invitation = $wpdb->get_row($wpdb->prepare(
            "SELECT i.*, t.created_by FROM {$wpdb->prefix}team_invitations i
             JOIN {$wpdb->prefix}teams t ON i.team_id = t.id
             WHERE i.id = %d AND i.status = 'pending'",
            $invitation_id
        ));

        if (!$invitation) {
            wp_send_json_error(array('message' => 'الدعوة غير موجودة'));
        }

        // Check if current user is team leader or the one who sent the invitation
        $current_user_id = get_current_user_id();
        if ($invitation->created_by != $current_user_id && $invitation->invited_by != $current_user_id) {
            wp_send_json_error(array('message' => 'غير مسموح لك بإلغاء هذه الدعوة'));
        }

        // Update invitation status
        $result = $wpdb->update(
            $wpdb->prefix . 'team_invitations',
            array('status' => 'cancelled'),
            array('id' => $invitation_id),
            array('%s'),
            array('%d')
        );

        if ($result) {
            wp_send_json_success(array('message' => 'تم إلغاء الدعوة'));
        } else {
            wp_send_json_error(array('message' => 'حدث خطأ أثناء إلغاء الدعوة'));
        }
    }

    /**
     * AJAX handler to check team slug availability
     */
    public function ajax_check_slug_availability() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'check_slug_nonce')) {
            wp_send_json_error(array('message' => 'Invalid nonce'));
        }

        $slug = sanitize_text_field($_POST['slug']);
        $current_team_id = isset($_POST['current_team_id']) ? intval($_POST['current_team_id']) : 0;

        // Validate slug format
        if (!$this->validate_team_slug($slug)) {
            wp_send_json_success(array(
                'available' => false,
                'message' => 'يُسمح بالأحرف الإنجليزية الصغيرة والأرقام والشرطة فقط',
                'type' => 'format_error'
            ));
        }

        // Check minimum length
        if (strlen($slug) < 3) {
            wp_send_json_success(array(
                'available' => false,
                'message' => 'يجب أن يكون الرابط 3 أحرف على الأقل',
                'type' => 'length_error'
            ));
        }

        // Check if slug is available
        global $wpdb;
        $query = "SELECT COUNT(*) FROM {$wpdb->prefix}teams WHERE slug = %s";
        $params = array($slug);

        // If editing existing team, exclude current team from check
        if ($current_team_id > 0) {
            $query .= " AND id != %d";
            $params[] = $current_team_id;
        }

        $exists = $wpdb->get_var($wpdb->prepare($query, $params));

        if ($exists == 0) {
            wp_send_json_success(array(
                'available' => true,
                'message' => 'الرابط متاح للاستخدام',
                'type' => 'available'
            ));
        } else {
            wp_send_json_success(array(
                'available' => false,
                'message' => 'هذا الرابط مستخدم بالفعل',
                'type' => 'taken'
            ));
        }
    }

    /**
     * Add team meta boxes for chapter post type
     */
    public function add_team_meta_boxes() {
        // Only add for chapter post type
        add_meta_box(
            'team_chapter_meta',
            'نشر كفريق',
            array($this, 'team_chapter_meta_box'),
            'chapter',
            'side',
            'high'
        );
    }

    /**
     * Display team meta box for chapters
     */
    public function team_chapter_meta_box($post) {
        // Get current user's teams
        $user_teams = $this->get_user_teams(get_current_user_id());
        $current_user_id = get_current_user_id();

        // Check if this chapter was published by a team and current user is a member
        $publish_as_team = get_post_meta($post->ID, '_publish_as_team', true);
        $selected_team = get_post_meta($post->ID, '_team_id', true);
        $team_members_roles = get_post_meta($post->ID, '_team_members_roles', true);

        // Check if current user is the original author (who can modify team settings)
        $is_original_author = ($post->post_author == $current_user_id);

        // If chapter is published by a team, check if current user is a member
        $can_edit_team_chapter = false;
        if ($publish_as_team === '1' && $selected_team) {
            $can_edit_team_chapter = $this->is_user_team_member($current_user_id, $selected_team);
        }

        // Show appropriate interface based on user's permissions
        if (empty($user_teams) && !$can_edit_team_chapter) {
            echo '<p>أنت لست عضواً في أي فريق حالياً.</p>';
            return;
        }

        wp_nonce_field('team_chapter_meta_nonce', 'team_chapter_meta_nonce');
        ?>
        <div id="team-chapter-meta">
            <?php if ($publish_as_team === '1' && $selected_team): ?>
                <!-- Show team info for existing team chapter -->
                <?php
                global $wpdb;
                $team = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM {$wpdb->prefix}teams WHERE id = %d",
                    $selected_team
                ));
                ?>
                <div class="team-chapter-info" style="background: #f0f8ff; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                    <h4 style="margin: 0 0 10px 0; color: #0073aa;">
                        <span class="dashicons dashicons-groups" style="margin-left: 5px;"></span>
                        منشور كفريق: <?php echo esc_html($team->name); ?>
                    </h4>
                    <?php if ($can_edit_team_chapter): ?>
                        <p style="margin: 0; color: #666; font-size: 13px;">
                            يمكنك تعديل هذا الفصل لأنك عضو في الفريق الذي نشره
                        </p>
                    <?php endif; ?>

                    <?php if (!$is_original_author): ?>
                        <p style="margin: 5px 0 0 0; color: #d63638; font-size: 12px;">
                            <span class="dashicons dashicons-lock" style="font-size: 12px; margin-left: 3px;"></span>
                            لا يمكنك تغيير إعدادات الفريق - فقط منشئ الفصل الأصلي يمكنه ذلك
                        </p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($user_teams) && ($is_original_author || $publish_as_team !== '1')): ?>
            <p>
                <label>
                    <input type="checkbox" id="publish_as_team" name="publish_as_team" value="1"
                           <?php checked($publish_as_team, '1'); ?>
                           <?php echo (!$is_original_author && $publish_as_team === '1') ? 'disabled' : ''; ?>>
                    أريد أن أنشر كفريق
                    <?php if (!$is_original_author && $publish_as_team === '1'): ?>
                        <span style="color: #d63638; font-size: 12px;">(محمي)</span>
                    <?php endif; ?>
                </label>
            </p>
            <?php endif; ?>

            <div id="team-selection" style="<?php echo $publish_as_team ? '' : 'display:none;'; ?>">
                <?php if ($is_original_author || $publish_as_team !== '1'): ?>
                    <!-- Original author can modify team settings -->
                    <p>
                        <label for="team_id">اختر الفريق:</label>
                        <select name="team_id" id="team_id">
                            <option value="">-- اختر فريق --</option>
                            <?php foreach ($user_teams as $team): ?>
                                <option value="<?php echo esc_attr($team->id); ?>" <?php selected($selected_team, $team->id); ?>>
                                    <?php echo esc_html($team->name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </p>

                    <div id="team-members-roles" style="<?php echo $selected_team ? '' : 'display:none;'; ?>">
                        <h4>طاقم العمل:</h4>
                        <div id="team-members-list">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Non-original authors see read-only team info -->
                    <?php if ($selected_team): ?>
                        <div style="background: #f9f9f9; padding: 10px; border-radius: 5px; border-left: 4px solid #d63638;">
                            <p style="margin: 0 0 10px 0; font-weight: bold;">
                                <span class="dashicons dashicons-lock" style="margin-left: 5px;"></span>
                                إعدادات الفريق محمية
                            </p>

                            <?php
                            global $wpdb;
                            $team = $wpdb->get_row($wpdb->prepare(
                                "SELECT * FROM {$wpdb->prefix}teams WHERE id = %d",
                                $selected_team
                            ));
                            ?>

                            <p style="margin: 0 0 10px 0;">
                                <strong>الفريق المحدد:</strong> <?php echo esc_html($team->name); ?>
                            </p>

                            <?php if (!empty($team_members_roles)): ?>
                                <div>
                                    <strong>طاقم العمل:</strong>
                                    <ul style="margin: 5px 0 0 20px;">
                                        <?php foreach ($team_members_roles as $user_id => $role): ?>
                                            <?php
                                            $user = get_userdata($user_id);
                                            if ($user):
                                            ?>
                                                <li><?php echo esc_html($user->display_name); ?> - <?php echo esc_html($role); ?></li>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <p style="margin: 10px 0 0 0; color: #666; font-size: 12px;">
                                فقط منشئ الفصل الأصلي يمكنه تعديل هذه الإعدادات
                            </p>
                        </div>

                        <!-- Hidden fields to preserve team settings -->
                        <input type="hidden" name="team_id" value="<?php echo esc_attr($selected_team); ?>">
                        <?php if (!empty($team_members_roles)): ?>
                            <?php foreach ($team_members_roles as $user_id => $role): ?>
                                <input type="hidden" name="team_members_roles[<?php echo esc_attr($user_id); ?>]" value="<?php echo esc_attr($role); ?>">
                            <?php endforeach; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            var isOriginalAuthor = <?php echo $is_original_author ? 'true' : 'false'; ?>;
            var isTeamChapter = <?php echo ($publish_as_team === '1') ? 'true' : 'false'; ?>;

            // Disable team settings for non-original authors of team chapters
            if (!isOriginalAuthor && isTeamChapter) {
                $('#publish_as_team, #team_id').prop('disabled', true);
                $('#team-members-roles select').prop('disabled', true);
            }

            // Toggle team selection
            $('#publish_as_team').change(function() {
                if ($(this).is(':checked') && !$(this).prop('disabled')) {
                    $('#team-selection').show();
                } else {
                    $('#team-selection').hide();
                    $('#team-members-roles').hide();
                }
            });

            // Load team members when team is selected
            $('#team_id').change(function() {
                if ($(this).prop('disabled')) return;

                var teamId = $(this).val();
                if (teamId) {
                    loadTeamMembers(teamId);
                    $('#team-members-roles').show();
                } else {
                    $('#team-members-roles').hide();
                }
            });

            // Load team members on page load if team is already selected
            var selectedTeam = $('#team_id').val();
            if (selectedTeam && !$('#team_id').prop('disabled')) {
                loadTeamMembers(selectedTeam);
            }

            function loadTeamMembers(teamId) {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'get_team_members',
                        team_id: teamId,
                        nonce: '<?php echo wp_create_nonce('team_members_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            var html = '';
                            var savedRoles = <?php echo json_encode($team_members_roles ?: array()); ?>;

                            $.each(response.data, function(index, member) {
                                var memberRole = savedRoles[member.ID] || '';
                                html += '<div class="team-member-role">';
                                html += '<label>' + member.display_name + ':</label>';
                                html += '<select name="team_members_roles[' + member.ID + ']">';
                                html += '<option value="">-- لم يشارك --</option>';
                                html += '<option value="translator"' + (memberRole === 'translator' ? ' selected' : '') + '>مترجم</option>';
                                html += '<option value="editor"' + (memberRole === 'editor' ? ' selected' : '') + '>مدقق لغوي</option>';
                                html += '<option value="designer"' + (memberRole === 'designer' ? ' selected' : '') + '>مصمم</option>';
                                html += '<option value="reviewer"' + (memberRole === 'reviewer' ? ' selected' : '') + '>مراجع جودة</option>';
                                html += '</select>';
                                html += '</div>';
                            });

                            $('#team-members-list').html(html);
                        }
                    }
                });
            }
        });
        </script>

        <style>
        .team-member-role {
            margin-bottom: 10px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .team-member-role label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .team-member-role select {
            width: 100%;
        }
        </style>
        <?php
    }

    /**
     * Save team chapter meta
     */
    public function save_team_chapter_meta($post_id) {
        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check post type
        if (get_post_type($post_id) !== 'chapter') {
            return;
        }

        // Check nonce
        if (!isset($_POST['team_chapter_meta_nonce']) || !wp_verify_nonce($_POST['team_chapter_meta_nonce'], 'team_chapter_meta_nonce')) {
            return;
        }

        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Get current post data
        $post = get_post($post_id);
        $current_user_id = get_current_user_id();
        $is_original_author = ($post->post_author == $current_user_id);

        // Get existing team settings
        $existing_publish_as_team = get_post_meta($post_id, '_publish_as_team', true);
        $existing_team_id = get_post_meta($post_id, '_team_id', true);
        $existing_team_members_roles = get_post_meta($post_id, '_team_members_roles', true);

        // Only original author can modify team settings
        if ($is_original_author) {
            // Save meta
            $publish_as_team = isset($_POST['publish_as_team']) ? '1' : '0';
            update_post_meta($post_id, '_publish_as_team', $publish_as_team);

            if ($publish_as_team === '1' && !empty($_POST['team_id'])) {
                update_post_meta($post_id, '_team_id', intval($_POST['team_id']));

                if (!empty($_POST['team_members_roles'])) {
                    $team_members_roles = array_map('sanitize_text_field', $_POST['team_members_roles']);
                    // Remove empty roles
                    $team_members_roles = array_filter($team_members_roles);
                    update_post_meta($post_id, '_team_members_roles', $team_members_roles);
                }
            } else {
                delete_post_meta($post_id, '_team_id');
                delete_post_meta($post_id, '_team_members_roles');
            }
        } else {
            // Non-original author: preserve existing team settings if chapter was published as team
            if ($existing_publish_as_team === '1') {
                // Keep existing team settings unchanged
                update_post_meta($post_id, '_publish_as_team', $existing_publish_as_team);
                if ($existing_team_id) {
                    update_post_meta($post_id, '_team_id', $existing_team_id);
                }
                if ($existing_team_members_roles) {
                    update_post_meta($post_id, '_team_members_roles', $existing_team_members_roles);
                }
            }
        }
    }

    /**
     * Get user teams
     */
    private function get_user_teams($user_id) {
        global $wpdb;

        return $wpdb->get_results($wpdb->prepare(
            "SELECT t.* FROM {$wpdb->prefix}teams t
             INNER JOIN {$wpdb->prefix}team_members tm ON t.id = tm.team_id
             WHERE tm.user_id = %d AND t.status = 'active'",
            $user_id
        ));
    }

    /**
     * AJAX handler to get team members
     */
    public function ajax_get_team_members() {
        check_ajax_referer('team_members_nonce', 'nonce');

        $team_id = isset($_POST['team_id']) ? intval($_POST['team_id']) : 0;

        if (!$team_id) {
            wp_send_json_error(array('message' => 'معرف الفريق مطلوب'));
        }

        global $wpdb;
        $members = $wpdb->get_results($wpdb->prepare(
            "SELECT u.ID, u.display_name
             FROM {$wpdb->prefix}team_members tm
             INNER JOIN {$wpdb->users} u ON tm.user_id = u.ID
             WHERE tm.team_id = %d
             ORDER BY u.display_name",
            $team_id
        ));

        wp_send_json_success($members);
    }

    /**
     * LAYER 1: Map team chapter capabilities (Highest Priority)
     * This is the first line of defense in WordPress capability system
     */
    public function map_team_chapter_capabilities($caps, $cap, $user_id, $args) {
        // Handle all edit-related capabilities
        $edit_caps = array('edit_post', 'edit_posts', 'edit_others_posts', 'edit_published_posts', 'edit_private_posts');

        if (!in_array($cap, $edit_caps) || empty($args[0])) {
            return $caps;
        }

        $post_id = $args[0];
        $post = get_post($post_id);

        // Only handle chapter post type
        if (!$post || $post->post_type !== 'chapter') {
            return $caps;
        }

        // Check if this is a team chapter
        if (!$this->is_team_chapter($post_id)) {
            return $caps;
        }

        $team_id = get_post_meta($post_id, '_team_id', true);
        if (!$team_id) {
            return $caps;
        }

        // Check team membership
        if ($this->is_user_team_member($user_id, $team_id)) {
            error_log("TEAM EDIT: map_meta_cap GRANTED - User: $user_id, Post: $post_id, Cap: $cap");
            // Return 'exist' which means "allow" in WordPress
            return array('exist');
        }

        error_log("TEAM EDIT: map_meta_cap DENIED - User: $user_id, Post: $post_id, Cap: $cap");
        return $caps;
    }

    /**
     * LAYER 2: Enhance team member capabilities (Very High Priority)
     * This directly modifies user capabilities
     */
    public function enhance_team_member_capabilities($allcaps, $caps, $args, $user) {
        if (!$user || !$user->ID) {
            return $allcaps;
        }

        // Handle specific post capabilities
        if (isset($args[0]) && isset($args[2])) {
            $capability = $args[0];
            $post_id = $args[2];

            // Only handle edit capabilities for chapters
            if (strpos($capability, 'edit') === false) {
                return $allcaps;
            }

            $post = get_post($post_id);
            if (!$post || $post->post_type !== 'chapter') {
                return $allcaps;
            }

            // Check if this is a team chapter
            if (!$this->is_team_chapter($post_id)) {
                return $allcaps;
            }

            $team_id = get_post_meta($post_id, '_team_id', true);
            if ($team_id && $this->is_user_team_member($user->ID, $team_id)) {
                // Grant comprehensive edit capabilities
                $allcaps['edit_post'] = true;
                $allcaps['edit_posts'] = true;
                $allcaps['edit_others_posts'] = true;
                $allcaps['edit_published_posts'] = true;
                $allcaps['edit_private_posts'] = true;
                $allcaps["edit_post_{$post_id}"] = true;

                error_log("TEAM EDIT: user_has_cap GRANTED - User: {$user->ID}, Post: $post_id");
                return $allcaps;
            }
        }

        return $allcaps;
    }

    /**
     * LAYER 3: Initialize team capabilities early
     */
    public function initialize_team_capabilities() {
        // This runs very early to set up the foundation
        if (!is_admin()) {
            return;
        }

        error_log("TEAM EDIT: Initializing team capabilities system");
    }

    /**
     * LAYER 4: Setup team member capabilities after WordPress loads
     */
    public function setup_team_member_capabilities() {
        if (!is_admin()) {
            return;
        }

        $current_user_id = get_current_user_id();
        if (!$current_user_id) {
            return;
        }

        // Get all team chapters this user can edit
        $editable_chapters = $this->get_user_editable_team_chapters($current_user_id);

        if (!empty($editable_chapters)) {
            $current_user = wp_get_current_user();

            // Grant capabilities for each chapter
            foreach ($editable_chapters as $chapter_id) {
                $current_user->allcaps["edit_post_{$chapter_id}"] = true;
            }

            // Grant general capabilities
            $current_user->allcaps['edit_posts'] = true;
            $current_user->allcaps['edit_others_posts'] = true;
            $current_user->allcaps['edit_published_posts'] = true;

            error_log("TEAM EDIT: Setup capabilities for user $current_user_id - " . count($editable_chapters) . " chapters");
        }
    }

    /**
     * LAYER 5: Admin-specific capability setup
     */
    public function setup_admin_team_capabilities() {
        if (!is_admin()) {
            return;
        }

        // This ensures capabilities are available in admin context
        $this->setup_team_member_capabilities();
    }

    /**
     * LAYER 6: Screen-specific capability setup
     */
    public function setup_screen_team_capabilities() {
        $screen = get_current_screen();
        if (!$screen || $screen->post_type !== 'chapter') {
            return;
        }

        // Ensure capabilities are set for chapter screens
        $this->setup_team_member_capabilities();

        error_log("TEAM EDIT: Screen capabilities setup for: " . $screen->id);
    }

    /**
     * Setup team edit capabilities for current screen
     * This is the most reliable way to ensure capabilities work
     */
    public function setup_team_edit_capabilities() {
        global $post, $pagenow;

        // Only on post edit pages
        if (!in_array($pagenow, array('post.php', 'post-new.php')) || !$post || $post->post_type !== 'chapter') {
            return;
        }

        // Only for existing posts (not new posts)
        if (!$post->ID) {
            return;
        }

        $current_user_id = get_current_user_id();

        // Check if this is a team chapter
        $publish_as_team = get_post_meta($post->ID, '_publish_as_team', true);
        if ($publish_as_team !== '1') {
            return;
        }

        $team_id = get_post_meta($post->ID, '_team_id', true);
        if (!$team_id) {
            return;
        }

        // Check if current user is team member
        if ($this->is_user_team_member($current_user_id, $team_id)) {
            // Grant capabilities directly to current user
            $current_user = wp_get_current_user();

            // Add all necessary capabilities
            $current_user->allcaps['edit_post'] = true;
            $current_user->allcaps['edit_posts'] = true;
            $current_user->allcaps['edit_others_posts'] = true;
            $current_user->allcaps['edit_published_posts'] = true;
            $current_user->allcaps['edit_private_posts'] = true;

            // Add specific post capability
            $current_user->allcaps["edit_post_{$post->ID}"] = true;

            error_log("Team capabilities granted directly to user {$current_user_id} for post {$post->ID}");

            // Also add a filter to bypass WordPress's built-in checks
            add_filter('user_can_edit_post', array($this, 'allow_team_member_edit'), 10, 3);
        }
    }

    /**
     * Allow team members to edit team posts - final fallback
     */
    public function allow_team_member_edit($can_edit, $post_id, $user_id) {
        // If already allowed, don't interfere
        if ($can_edit) {
            return $can_edit;
        }

        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'chapter') {
            return $can_edit;
        }

        // Check if this is a team chapter
        $publish_as_team = get_post_meta($post_id, '_publish_as_team', true);
        if ($publish_as_team !== '1') {
            return $can_edit;
        }

        $team_id = get_post_meta($post_id, '_team_id', true);
        if (!$team_id) {
            return $can_edit;
        }

        // Final check - if user is team member, allow edit
        if ($this->is_user_team_member($user_id, $team_id)) {
            error_log("Final fallback - allowing team member {$user_id} to edit post {$post_id}");
            return true;
        }

        return $can_edit;
    }

    /**
     * Setup global team capabilities for all team members
     */
    public function setup_global_team_capabilities() {
        $current_user_id = get_current_user_id();
        if (!$current_user_id) {
            return;
        }

        // Get all teams user is member of
        global $wpdb;
        $user_teams = $wpdb->get_results($wpdb->prepare(
            "SELECT DISTINCT team_id FROM {$wpdb->prefix}team_members
             WHERE user_id = %d
             AND (status = 'active' OR status IS NULL OR is_active = 1)",
            $current_user_id
        ));

        if (empty($user_teams)) {
            return;
        }

        $team_ids = array_map(function($team) { return $team->team_id; }, $user_teams);
        $team_ids_placeholder = implode(',', array_fill(0, count($team_ids), '%d'));

        // Get all team chapter IDs
        $team_chapter_ids = $wpdb->get_col($wpdb->prepare(
            "SELECT DISTINCT p.ID
             FROM {$wpdb->posts} p
             INNER JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_publish_as_team' AND pm1.meta_value = '1'
             INNER JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_team_id'
             WHERE p.post_type = 'chapter'
             AND pm2.meta_value IN ($team_ids_placeholder)",
            ...$team_ids
        ));

        if (!empty($team_chapter_ids)) {
            $current_user = wp_get_current_user();

            // Grant capabilities for each team chapter
            foreach ($team_chapter_ids as $chapter_id) {
                $current_user->allcaps["edit_post_{$chapter_id}"] = true;
            }

            // Grant general capabilities
            $current_user->allcaps['edit_posts'] = true;
            $current_user->allcaps['edit_others_posts'] = true;
            $current_user->allcaps['edit_published_posts'] = true;

            error_log("Global team capabilities granted for user {$current_user_id} - " . count($team_chapter_ids) . " chapters");
        }
    }

    /**
     * Add edit links for team chapters in posts list
     */
    public function add_team_edit_links($actions, $post) {
        if ($post->post_type !== 'chapter') {
            return $actions;
        }

        // Check if this is a team chapter
        $publish_as_team = get_post_meta($post->ID, '_publish_as_team', true);
        if ($publish_as_team !== '1') {
            return $actions;
        }

        $team_id = get_post_meta($post->ID, '_team_id', true);
        if (!$team_id) {
            return $actions;
        }

        $current_user_id = get_current_user_id();

        // Check if current user is team member
        if ($this->is_user_team_member($current_user_id, $team_id)) {
            // If edit link doesn't exist, add it
            if (!isset($actions['edit'])) {
                $actions['edit'] = sprintf(
                    '<a href="%s" aria-label="%s">%s</a>',
                    get_edit_post_link($post->ID),
                    esc_attr(sprintf(__('Edit &#8220;%s&#8221;'), $post->post_title)),
                    __('Edit')
                );
            }

            // Add team indicator
            if (isset($actions['edit'])) {
                $actions['edit'] .= ' <span style="color: #0073aa;">(فريق)</span>';
            }
        }

        return $actions;
    }

    /**
     * Helper: Check if a post is a team chapter
     */
    private function is_team_chapter($post_id) {
        $publish_as_team = get_post_meta($post_id, '_publish_as_team', true);
        return $publish_as_team === '1';
    }

    /**
     * Helper: Check if user is a member of a specific team
     */
    private function is_user_team_member($user_id, $team_id) {
        global $wpdb;

        // First, let's check what columns exist in the team_members table
        $columns = $wpdb->get_col("DESCRIBE {$wpdb->prefix}team_members");
        error_log("TEAM EDIT: team_members table columns: " . implode(', ', $columns));

        // Build query based on available columns
        $where_conditions = array("user_id = %d", "team_id = %d");
        $query_params = array($user_id, $team_id);

        // Check for status column variations
        if (in_array('status', $columns)) {
            $where_conditions[] = "(status = 'active' OR status IS NULL)";
        } elseif (in_array('is_active', $columns)) {
            $where_conditions[] = "(is_active = 1 OR is_active IS NULL)";
        }

        $query = "SELECT COUNT(*) FROM {$wpdb->prefix}team_members WHERE " . implode(' AND ', $where_conditions);

        $is_member = $wpdb->get_var($wpdb->prepare($query, ...$query_params));

        // Enhanced debug logging
        error_log("TEAM EDIT: Membership check - User: $user_id, Team: $team_id");
        error_log("TEAM EDIT: Query: " . $wpdb->last_query);
        error_log("TEAM EDIT: Result: " . ($is_member > 0 ? 'YES' : 'NO'));

        // Additional debug: Show all team members for this team
        $all_members = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}team_members WHERE team_id = %d",
            $team_id
        ));
        error_log("TEAM EDIT: All members for team $team_id: " . print_r($all_members, true));

        return $is_member > 0;
    }

    /**
     * Helper: Get team ID for a chapter
     */
    private function get_chapter_team_id($post_id) {
        if (!$this->is_team_chapter($post_id)) {
            return false;
        }
        return get_post_meta($post_id, '_team_id', true);
    }

    /**
     * Helper: Get all team chapters a user can edit
     */
    private function get_user_editable_team_chapters($user_id) {
        global $wpdb;

        // Check table structure first
        $columns = $wpdb->get_col("DESCRIBE {$wpdb->prefix}team_members");

        // Build query based on available columns
        $where_conditions = array("user_id = %d");
        $query_params = array($user_id);

        // Check for status column variations
        if (in_array('status', $columns)) {
            $where_conditions[] = "(status = 'active' OR status IS NULL)";
        } elseif (in_array('is_active', $columns)) {
            $where_conditions[] = "(is_active = 1 OR is_active IS NULL)";
        }

        $query = "SELECT DISTINCT team_id FROM {$wpdb->prefix}team_members WHERE " . implode(' AND ', $where_conditions);

        // Get user's teams
        $user_teams = $wpdb->get_results($wpdb->prepare($query, ...$query_params));

        error_log("TEAM EDIT: User $user_id teams query: " . $wpdb->last_query);
        error_log("TEAM EDIT: User teams found: " . print_r($user_teams, true));

        if (empty($user_teams)) {
            error_log("TEAM EDIT: No teams found for user $user_id");
            return array();
        }

        $team_ids = array_map(function($team) { return $team->team_id; }, $user_teams);
        $team_ids_placeholder = implode(',', array_fill(0, count($team_ids), '%d'));

        // Get all team chapters for these teams
        $chapter_ids = $wpdb->get_col($wpdb->prepare(
            "SELECT DISTINCT p.ID
             FROM {$wpdb->posts} p
             INNER JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_publish_as_team' AND pm1.meta_value = '1'
             INNER JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_team_id'
             WHERE p.post_type = 'chapter'
             AND pm2.meta_value IN ($team_ids_placeholder)",
            ...$team_ids
        ));

        error_log("TEAM EDIT: Team chapters found for user $user_id: " . print_r($chapter_ids, true));

        return $chapter_ids;
    }

    /**
     * Show admin notices for team chapter editing
     */
    public function show_team_chapter_edit_notices() {
        global $post, $pagenow;

        // Only show on chapter edit pages
        if ($pagenow !== 'post.php' || !$post || $post->post_type !== 'chapter') {
            return;
        }

        // Check if this chapter was published as a team
        $publish_as_team = get_post_meta($post->ID, '_publish_as_team', true);
        if ($publish_as_team !== '1') {
            return;
        }

        $team_id = get_post_meta($post->ID, '_team_id', true);
        if (!$team_id) {
            return;
        }

        $current_user_id = get_current_user_id();

        // Check if current user is a member of the team
        if ($this->is_user_team_member($current_user_id, $team_id)) {
            // Show success notice for team members
            global $wpdb;
            $team = $wpdb->get_row($wpdb->prepare(
                "SELECT name FROM {$wpdb->prefix}teams WHERE id = %d",
                $team_id
            ));

            if ($team) {
                echo '<div class="notice notice-info is-dismissible">';
                echo '<p><strong>معلومة:</strong> هذا الفصل منشور كفريق "' . esc_html($team->name) . '". يمكنك تعديله لأنك عضو في هذا الفريق.</p>';
                echo '</div>';
            }
        } else {
            // Show warning for non-team members
            global $wpdb;
            $team = $wpdb->get_row($wpdb->prepare(
                "SELECT name FROM {$wpdb->prefix}teams WHERE id = %d",
                $team_id
            ));

            if ($team) {
                echo '<div class="notice notice-warning">';
                echo '<p><strong>تنبيه:</strong> هذا الفصل منشور كفريق "' . esc_html($team->name) . '". لا يمكنك تعديله إلا إذا كنت عضواً في هذا الفريق.</p>';
                echo '</div>';
            }
        }
    }

    /**
     * Add team column to chapters list
     */
    public function add_team_column_to_chapters($columns) {
        // Add team column after title
        $new_columns = array();
        foreach ($columns as $key => $value) {
            $new_columns[$key] = $value;
            if ($key === 'title') {
                $new_columns['team'] = 'الفريق';
            }
        }
        return $new_columns;
    }

    /**
     * Display team column content
     */
    public function display_team_column_content($column, $post_id) {
        if ($column === 'team') {
            $publish_as_team = get_post_meta($post_id, '_publish_as_team', true);

            if ($publish_as_team === '1') {
                $team_id = get_post_meta($post_id, '_team_id', true);

                if ($team_id) {
                    global $wpdb;
                    $team = $wpdb->get_row($wpdb->prepare(
                        "SELECT name, slug FROM {$wpdb->prefix}teams WHERE id = %d",
                        $team_id
                    ));

                    if ($team) {
                        $team_url = home_url('/teams/' . $team->slug);
                        echo '<span class="team-badge" style="background: #0073aa; color: white; padding: 3px 8px; border-radius: 3px; font-size: 12px; display: inline-block;">';
                        echo '<span class="dashicons dashicons-groups" style="font-size: 12px; margin-left: 3px; vertical-align: middle;"></span>';
                        echo '<a href="' . esc_url($team_url) . '" style="color: white; text-decoration: none;" target="_blank">' . esc_html($team->name) . '</a>';
                        echo '</span>';

                        // Show if current user is team member (always show for team chapters)
                        $current_user_id = get_current_user_id();
                        $is_user_team_member = $this->is_user_team_member($current_user_id, $team_id);

                        if ($is_user_team_member) {
                            echo '<br><small style="color: #46b450;">✓ عضو في الفريق</small>';

                            // Show team members roles if available
                            $team_members_roles = get_post_meta($post_id, '_team_members_roles', true);
                            if (!empty($team_members_roles) && isset($team_members_roles[$current_user_id])) {
                                echo '<br><small style="color: #666;">دورك: ' . esc_html($team_members_roles[$current_user_id]) . '</small>';
                            }
                        }
                    } else {
                        echo '<span style="color: #999;">فريق محذوف</span>';
                    }
                } else {
                    echo '<span style="color: #999;">خطأ في البيانات</span>';
                }
            } else {
                echo '<span style="color: #999;">—</span>';
            }
        }
    }

    /**
     * Enqueue admin scripts for team functionality
     */
    public function enqueue_team_admin_scripts($hook) {
        global $post_type;

        if ($post_type === 'chapter' && ($hook === 'post.php' || $hook === 'post-new.php')) {
            wp_enqueue_script('jquery');
            wp_localize_script('jquery', 'team_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('team_members_nonce')
            ));
        }
    }

    /**
     * Add admin bar notification for pending invitations
     */
    public function add_admin_bar_notification($wp_admin_bar) {
        if (!is_user_logged_in()) {
            return;
        }

        global $wpdb;
        $current_user_id = get_current_user_id();

        // Get pending invitations count
        $pending_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}team_invitations
             WHERE user_id = %d AND status = 'pending' AND expires_at > NOW()",
            $current_user_id
        ));

        if ($pending_count > 0) {
            $wp_admin_bar->add_node(array(
                'id' => 'team-invitations',
                'title' => sprintf(
                    '<span class="ab-icon dashicons dashicons-groups"></span><span class="ab-label">الفرق</span><span class="awaiting-mod count-%d"><span class="pending-count">%d</span></span>',
                    $pending_count,
                    $pending_count
                ),
                'href' => admin_url('admin.php?page=team-system'),
                'meta' => array(
                    'title' => sprintf('لديك %d دعوة معلقة للانضمام للفرق', $pending_count)
                )
            ));
        } else {
            // Show regular teams menu without notification
            $wp_admin_bar->add_node(array(
                'id' => 'team-system',
                'title' => '<span class="ab-icon dashicons dashicons-groups"></span><span class="ab-label">الفرق</span>',
                'href' => admin_url('admin.php?page=team-system'),
                'meta' => array(
                    'title' => 'إدارة الفرق'
                )
            ));
        }
    }

    /**
     * Include team chapters in "mine" filter
     */
    public function include_team_chapters_in_mine($query) {
        global $pagenow, $wpdb;

        // Only apply on admin chapter list page
        if (!is_admin() || $pagenow !== 'edit.php' || !isset($_GET['post_type']) || $_GET['post_type'] !== 'chapter') {
            return;
        }

        // Only modify when showing "mine" (author filter)
        $author = $query->get('author');
        $current_user_id = get_current_user_id();

        // If author filter is set and it's for current user, include team chapters
        if ($author && $author == $current_user_id) {
            // Get user's teams
            $user_teams = $wpdb->get_results($wpdb->prepare(
                "SELECT DISTINCT t.id
                 FROM {$wpdb->prefix}teams t
                 INNER JOIN {$wpdb->prefix}team_members tm ON t.id = tm.team_id
                 WHERE tm.user_id = %d
                 AND t.status = 'active'
                 AND (tm.status = 'active' OR tm.status IS NULL OR tm.is_active = 1)",
                $current_user_id
            ));

            if (!empty($user_teams)) {
                $team_ids = array_map(function($team) { return $team->id; }, $user_teams);

                // Get team chapter IDs
                $team_ids_placeholder = implode(',', array_fill(0, count($team_ids), '%d'));
                $team_chapter_ids = $wpdb->get_col($wpdb->prepare(
                    "SELECT DISTINCT p.ID
                     FROM {$wpdb->posts} p
                     INNER JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_publish_as_team' AND pm1.meta_value = '1'
                     INNER JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_team_id'
                     WHERE p.post_type = 'chapter'
                     AND pm2.meta_value IN ($team_ids_placeholder)",
                    ...$team_ids
                ));

                // Get user's own posts
                $user_posts = $wpdb->get_col($wpdb->prepare(
                    "SELECT ID FROM {$wpdb->posts}
                     WHERE post_type = 'chapter' AND post_author = %d",
                    $current_user_id
                ));

                // Combine both arrays
                $all_post_ids = array_unique(array_merge($user_posts, $team_chapter_ids));

                if (!empty($all_post_ids)) {
                    // Remove author filter and use post__in instead
                    $query->set('author', '');
                    $query->set('post__in', $all_post_ids);

                    error_log('Team Integration Debug - User posts: ' . count($user_posts));
                    error_log('Team Integration Debug - Team posts: ' . count($team_chapter_ids));
                    error_log('Team Integration Debug - Total posts: ' . count($all_post_ids));
                }
            }
        }
    }

    /**
     * LAYER 7: Ensure team edit links appear in post list
     */
    public function ensure_team_edit_links($actions, $post) {
        if ($post->post_type !== 'chapter') {
            return $actions;
        }

        // Check if this is a team chapter
        if (!$this->is_team_chapter($post->ID)) {
            return $actions;
        }

        $team_id = $this->get_chapter_team_id($post->ID);
        if (!$team_id) {
            return $actions;
        }

        $current_user_id = get_current_user_id();

        // Check if current user is team member
        if ($this->is_user_team_member($current_user_id, $team_id)) {
            // Force add edit link if it doesn't exist
            if (!isset($actions['edit'])) {
                $edit_link = get_edit_post_link($post->ID);
                if ($edit_link) {
                    $actions['edit'] = sprintf(
                        '<a href="%s" aria-label="%s">%s</a>',
                        esc_url($edit_link),
                        esc_attr(sprintf('Edit "%s"', $post->post_title)),
                        'Edit'
                    );
                }
            }

            // Add team indicator
            if (isset($actions['edit'])) {
                $actions['edit'] .= ' <span style="color: #0073aa; font-weight: bold;">(Team)</span>';
            }

            error_log("TEAM EDIT: Edit link ensured for user $current_user_id on post {$post->ID}");
        }

        return $actions;
    }

    /**
     * LAYER 8: Direct edit permission override (Final Layer)
     */
    public function allow_team_chapter_editing($can_edit, $post_id, $user_id) {
        // If already allowed, don't interfere
        if ($can_edit) {
            return $can_edit;
        }

        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'chapter') {
            return $can_edit;
        }

        // Check if this is a team chapter
        if (!$this->is_team_chapter($post_id)) {
            return $can_edit;
        }

        $team_id = $this->get_chapter_team_id($post_id);
        if (!$team_id) {
            return $can_edit;
        }

        // Final check - if user is team member, allow edit
        if ($this->is_user_team_member($user_id, $team_id)) {
            error_log("TEAM EDIT: FINAL OVERRIDE - User $user_id can edit post $post_id");
            return true;
        }

        return $can_edit;
    }

} // End of class