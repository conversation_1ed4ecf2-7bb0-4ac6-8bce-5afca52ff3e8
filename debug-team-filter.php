<?php
/**
 * Debug file for team filter issues
 * Add this to wp-admin to debug the team filter
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add debug function
function debug_team_filter() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    global $wpdb;
    $current_user_id = get_current_user_id();
    
    echo "<h2>تشخيص مشاكل فلتر الفريق</h2>";

    // 0. Check table structure first
    echo "<h3>0. بنية جدول team_members</h3>";
    $columns = $wpdb->get_col("DESCRIBE {$wpdb->prefix}team_members");
    echo "<p><strong>الأعمدة الموجودة:</strong> " . implode(', ', $columns) . "</p>";

    // Show sample data
    $sample_data = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}team_members LIMIT 5");
    if (!empty($sample_data)) {
        echo "<h4>عينة من البيانات:</h4>";
        echo "<table border='1'>";
        echo "<tr>";
        foreach ($columns as $col) {
            echo "<th>$col</th>";
        }
        echo "</tr>";
        foreach ($sample_data as $row) {
            echo "<tr>";
            foreach ($columns as $col) {
                echo "<td>" . (isset($row->$col) ? $row->$col : 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }

    // 1. Check user's teams
    echo "<h3>1. فرق المستخدم الحالي (ID: $current_user_id)</h3>";

    // Try different query variations based on table structure
    if (in_array('status', $columns)) {
        $user_teams = $wpdb->get_results($wpdb->prepare(
            "SELECT t.*, tm.role, tm.status
             FROM {$wpdb->prefix}teams t
             INNER JOIN {$wpdb->prefix}team_members tm ON t.id = tm.team_id
             WHERE tm.user_id = %d",
            $current_user_id
        ));
    } elseif (in_array('is_active', $columns)) {
        $user_teams = $wpdb->get_results($wpdb->prepare(
            "SELECT t.*, tm.role, tm.is_active
             FROM {$wpdb->prefix}teams t
             INNER JOIN {$wpdb->prefix}team_members tm ON t.id = tm.team_id
             WHERE tm.user_id = %d",
            $current_user_id
        ));
    } else {
        $user_teams = $wpdb->get_results($wpdb->prepare(
            "SELECT t.*, tm.role
             FROM {$wpdb->prefix}teams t
             INNER JOIN {$wpdb->prefix}team_members tm ON t.id = tm.team_id
             WHERE tm.user_id = %d",
            $current_user_id
        ));
    }

    echo "<p><strong>الاستعلام المستخدم:</strong> " . $wpdb->last_query . "</p>";
    
    if (empty($user_teams)) {
        echo "<p style='color: red;'>المستخدم ليس عضواً في أي فريق!</p>";
        return;
    }
    
    echo "<table border='1'>";
    echo "<tr><th>اسم الفريق</th><th>معرف الفريق</th><th>الدور</th><th>الحالة</th></tr>";
    foreach ($user_teams as $team) {
        echo "<tr>";
        echo "<td>" . esc_html($team->name) . "</td>";
        echo "<td>" . $team->id . "</td>";
        echo "<td>" . esc_html($team->role) . "</td>";
        echo "<td>" . esc_html($team->status ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. Check team chapters
    $team_ids = array_map(function($team) { return $team->id; }, $user_teams);
    $team_ids_placeholder = implode(',', array_fill(0, count($team_ids), '%d'));
    
    echo "<h3>2. الفصول المنشورة كفريق</h3>";
    $team_chapters = $wpdb->get_results($wpdb->prepare(
        "SELECT p.ID, p.post_title, p.post_author, p.post_status, pm2.meta_value as team_id
         FROM {$wpdb->posts} p
         INNER JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_publish_as_team' AND pm1.meta_value = '1'
         INNER JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_team_id'
         WHERE p.post_type = 'chapter' 
         AND pm2.meta_value IN ($team_ids_placeholder)
         ORDER BY p.post_date DESC",
        ...$team_ids
    ));
    
    if (empty($team_chapters)) {
        echo "<p style='color: orange;'>لا توجد فصول منشورة كفريق للفرق التي أنت عضو فيها</p>";
    } else {
        echo "<table border='1'>";
        echo "<tr><th>عنوان الفصل</th><th>معرف الفصل</th><th>المؤلف</th><th>الحالة</th><th>معرف الفريق</th></tr>";
        foreach ($team_chapters as $chapter) {
            $author = get_userdata($chapter->post_author);
            echo "<tr>";
            echo "<td>" . esc_html($chapter->post_title) . "</td>";
            echo "<td>" . $chapter->ID . "</td>";
            echo "<td>" . esc_html($author->display_name ?? 'غير معروف') . "</td>";
            echo "<td>" . esc_html($chapter->post_status) . "</td>";
            echo "<td>" . $chapter->team_id . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 3. Check all team chapters (not just user's teams)
    echo "<h3>3. جميع الفصول المنشورة كفريق في النظام</h3>";
    $all_team_chapters = $wpdb->get_results(
        "SELECT p.ID, p.post_title, p.post_author, p.post_status, pm2.meta_value as team_id, t.name as team_name
         FROM {$wpdb->posts} p
         INNER JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_publish_as_team' AND pm1.meta_value = '1'
         INNER JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key = '_team_id'
         LEFT JOIN {$wpdb->prefix}teams t ON t.id = pm2.meta_value
         WHERE p.post_type = 'chapter'
         ORDER BY p.post_date DESC
         LIMIT 20"
    );
    
    if (!empty($all_team_chapters)) {
        echo "<table border='1'>";
        echo "<tr><th>عنوان الفصل</th><th>المؤلف</th><th>الحالة</th><th>اسم الفريق</th></tr>";
        foreach ($all_team_chapters as $chapter) {
            $author = get_userdata($chapter->post_author);
            echo "<tr>";
            echo "<td>" . esc_html($chapter->post_title) . "</td>";
            echo "<td>" . esc_html($author->display_name ?? 'غير معروف') . "</td>";
            echo "<td>" . esc_html($chapter->post_status) . "</td>";
            echo "<td>" . esc_html($chapter->team_name ?? 'فريق محذوف') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 4. Check capabilities
    echo "<h3>4. صلاحيات المستخدم</h3>";
    $user = wp_get_current_user();
    echo "<p><strong>الأدوار:</strong> " . implode(', ', $user->roles) . "</p>";
    echo "<p><strong>الصلاحيات المهمة:</strong></p>";
    echo "<ul>";
    echo "<li>edit_posts: " . (current_user_can('edit_posts') ? '✓' : '✗') . "</li>";
    echo "<li>edit_others_posts: " . (current_user_can('edit_others_posts') ? '✓' : '✗') . "</li>";
    echo "<li>edit_published_posts: " . (current_user_can('edit_published_posts') ? '✓' : '✗') . "</li>";
    echo "</ul>";
}

// Add admin page
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            'tools.php',
            'تشخيص فلتر الفريق',
            'تشخيص فلتر الفريق',
            'manage_options',
            'debug-team-filter',
            'debug_team_filter'
        );
    }
});
