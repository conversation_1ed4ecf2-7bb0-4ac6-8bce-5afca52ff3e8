<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Team_System_DB {

    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        $sql_teams = "CREATE TABLE {$wpdb->prefix}teams (
            team_id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            logo_url VARCHAR(255),
            social_links TEXT,
            created_by BIGINT UNSIGNED NOT NULL,
            created_at DATETIME NOT NULL,
            PRIMARY KEY  (team_id)
        ) $charset_collate;";

        $sql_members = "CREATE TABLE {$wpdb->prefix}team_members (
            member_id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
            team_id BIGINT UNSIGNED NOT NULL,
            user_id BIGINT UNSIGNED NOT NULL,
            role ENUM('translator', 'reviewer', 'graphics', 'quality') NOT NULL,
            joined_at DATETIME NOT NULL,
            <PERSON><PERSON>AR<PERSON> KEY  (member_id),
            <PERSON>OR<PERSON><PERSON><PERSON> KEY (team_id) REFERENCES {$wpdb->prefix}teams(team_id) ON DELETE CASCADE
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta($sql_teams);
        dbDelta($sql_members);
    }
}
