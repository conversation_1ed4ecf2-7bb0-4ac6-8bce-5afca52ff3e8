<?php
/**
 * Database update script for Team System
 * This file updates the database structure to include missing social media columns
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

function team_system_update_database() {
    global $wpdb;
    
    $table_teams = $wpdb->prefix . 'teams';
    
    // Check if table exists
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_teams'") != $table_teams) {
        return false;
    }
    
    // Add social media columns if they don't exist
    $social_columns = array(
        'website_url' => 'VARCHAR(255) DEFAULT NULL',
        'facebook_url' => 'VARCHAR(255) DEFAULT NULL', 
        'twitter_url' => 'VARCHAR(255) DEFAULT NULL',
        'discord_url' => 'VARCHAR(255) DEFAULT NULL'
    );
    
    $updated = false;
    
    foreach ($social_columns as $column => $definition) {
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_teams LIKE '$column'");
        if (empty($column_exists)) {
            $result = $wpdb->query("ALTER TABLE $table_teams ADD COLUMN $column $definition");
            if ($result !== false) {
                $updated = true;
                error_log("Team System: Added column $column to $table_teams");
            } else {
                error_log("Team System: Failed to add column $column to $table_teams - " . $wpdb->last_error);
            }
        }
    }
    
    return $updated;
}

// Run the update if this file is called directly from admin
if (is_admin() && current_user_can('manage_options')) {
    team_system_update_database();
}
